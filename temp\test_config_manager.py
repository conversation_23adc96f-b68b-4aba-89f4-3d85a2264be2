#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ConfigSyncManager功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_import.config_sync_manager import ConfigSyncManager
import json

def test_config_manager():
    """测试ConfigSyncManager功能"""
    print('=== 测试ConfigSyncManager初始化 ===')
    try:
        config_manager = ConfigSyncManager()
        print('✅ ConfigSyncManager初始化成功')
        
        # 测试加载配置
        print('\n=== 测试配置加载 ===')
        config = config_manager._load_config_file()
        print(f'✅ 配置文件加载成功，版本: {config.get("version")}')
        print(f'✅ 字段模板数量: {len(config.get("field_templates", {}))}')
        
        # 测试模板获取
        print('\n=== 测试模板获取 ===')
        template = config_manager.get_template_mapping('离休人员工资表')
        if template:
            print(f'✅ 离休人员工资表模板加载成功，字段数量: {len(template)}')
            print(f'   示例字段: {list(template.items())[:3]}')
        else:
            print('❌ 离休人员工资表模板加载失败')
        
        # 测试所有模板
        print('\n=== 测试所有模板 ===')
        templates = config.get("field_templates", {})
        for template_name in templates.keys():
            template = config_manager.get_template_mapping(template_name)
            if template:
                print(f'✅ {template_name}: {len(template)} 个字段')
            else:
                print(f'❌ {template_name}: 加载失败')
        
        # 测试模板应用
        print('\n=== 测试模板应用 ===')
        test_table_name = "salary_data_2025_01_retired_employees"
        success = config_manager.apply_template_to_table(test_table_name, "退休人员工资表")
        if success:
            print(f'✅ 成功将退休人员工资表模板应用到 {test_table_name}')
            
            # 验证应用结果
            mapping = config_manager.load_mapping(test_table_name)
            if mapping:
                print(f'✅ 验证成功，映射包含 {len(mapping)} 个字段')
                print(f'   示例映射: {list(mapping.items())[:3]}')
            else:
                print('❌ 验证失败，无法加载映射')
        else:
            print('❌ 模板应用失败')
            
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_config_manager()
