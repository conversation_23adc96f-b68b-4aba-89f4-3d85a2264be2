#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复分页字段映射问题和创建差异化字段映射
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.log_config import setup_logger

def create_differentiated_field_mappings():
    """为不同类型的工资表创建差异化的字段映射"""
    
    # 基础字段映射（所有表共有的）
    base_mapping = {
        "id": "ID",
        "employee_id": "工号",
        "employee_name": "姓名",
        "month": "月份",
        "year": "年份",
        "created_at": "创建时间",
        "updated_at": "更新时间"
    }
    
    # 不同类型表的特殊字段映射
    type_specific_mappings = {
        "active_employees": {  # 全部在职人员
            **base_mapping,
            "id_card": "身份证号",
            "department": "部门",
            "position": "职位",
            "basic_salary": "基本工资",
            "performance_bonus": "绩效奖金",
            "overtime_pay": "加班费",
            "allowance": "津贴补贴",
            "deduction": "扣款",
            "total_salary": "应发合计"
        },
        "a_grade_employees": {  # A岗职工
            **base_mapping,
            "id_card": "身份证号",
            "department": "部门",
            "position": "岗位",
            "basic_salary": "岗位工资",
            "performance_bonus": "绩效工资",
            "overtime_pay": "加班费",
            "allowance": "岗位津贴",
            "deduction": "扣款",
            "total_salary": "岗位合计"
        },
        "retired_employees": {  # 退休人员
            **base_mapping,
            "id_card": "身份证号",
            "department": "原部门",
            "position": "原职位",
            "basic_salary": "退休金",
            "performance_bonus": "补发金额",
            "overtime_pay": "其他补贴",
            "allowance": "生活补贴",
            "deduction": "代扣费用",
            "total_salary": "实发金额"
        },
        "pension_employees": {  # 养老保险
            **base_mapping,
            "id_card": "身份证号",
            "department": "部门",
            "position": "职位",
            "basic_salary": "缴费基数",
            "performance_bonus": "补充保险",
            "overtime_pay": "其他保险",
            "allowance": "单位缴费",
            "deduction": "个人缴费",
            "total_salary": "保险合计"
        }
    }
    
    return type_specific_mappings

def identify_table_type(table_name):
    """根据表名识别表类型"""
    if "active_employees" in table_name:
        return "active_employees"
    elif "a_grade_employees" in table_name:
        return "a_grade_employees"
    elif "retired_employees" in table_name:
        return "retired_employees"
    elif "pension_employees" in table_name:
        return "pension_employees"
    else:
        return "active_employees"  # 默认类型

def main():
    """主函数"""
    logger = setup_logger(__name__)
    
    try:
        # 初始化表管理器
        table_manager = DynamicTableManager()
        
        print("=== 修复分页字段映射问题和创建差异化字段映射 ===\n")
        
        # 获取所有工资数据表
        all_tables = table_manager.get_table_list(table_type='salary_data')
        salary_tables = [t['table_name'] for t in all_tables if isinstance(t, dict) and 'table_name' in t]
        
        print(f"发现 {len(salary_tables)} 个工资数据表")
        
        # 加载现有映射配置
        mapping_file = "state/data/field_mappings.json"
        if os.path.exists(mapping_file):
            with open(mapping_file, 'r', encoding='utf-8') as f:
                mappings = json.load(f)
        else:
            print(f"❌ 映射配置文件不存在: {mapping_file}")
            return
        
        # 获取差异化字段映射模板
        type_mappings = create_differentiated_field_mappings()
        
        # 为每个表创建差异化映射
        updated_count = 0
        type_counts = {"active_employees": 0, "a_grade_employees": 0, "retired_employees": 0, "pension_employees": 0}
        
        for table_name in salary_tables:
            print(f"\n处理表: {table_name}")
            
            # 检查表是否存在
            if not table_manager.table_exists(table_name):
                print(f"  ❌ 表不存在，跳过")
                continue
            
            # 识别表类型
            table_type = identify_table_type(table_name)
            type_counts[table_type] += 1
            
            print(f"  表类型: {table_type}")
            
            # 获取表字段
            columns_info = table_manager.get_table_columns(table_name)
            actual_columns = [col['name'] for col in columns_info]
            
            # 获取对应类型的字段映射模板
            template_mapping = type_mappings[table_type]
            
            # 创建该表的字段映射（只包含实际存在的字段）
            table_mapping = {}
            for col in actual_columns:
                if col in template_mapping:
                    table_mapping[col] = template_mapping[col]
                else:
                    # 对于模板中没有的字段，保持原名
                    table_mapping[col] = col
            
            # 保存到映射配置中
            mappings["table_mappings"][table_name] = {
                "field_mappings": table_mapping,
                "original_excel_headers": {},
                "metadata": {
                    "source": "differentiated_fix",
                    "table_type": table_type,
                    "auto_generated": True,
                    "user_modified": False,
                    "created_at": "2025-06-27T15:20:00.000000",
                    "has_chinese_headers": True
                }
            }
            
            print(f"  ✅ 已创建差异化字段映射: {len(table_mapping)} 个字段")
            updated_count += 1
        
        # 更新时间戳
        from datetime import datetime
        mappings["last_updated"] = datetime.now().isoformat()
        
        # 保存映射配置
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mappings, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 差异化字段映射创建完成！")
        print(f"   - 处理了 {updated_count} 个表")
        print(f"   - 配置文件已保存: {mapping_file}")
        print(f"\n各类型表统计:")
        for table_type, count in type_counts.items():
            type_name = {
                "active_employees": "全部在职人员",
                "a_grade_employees": "A岗职工", 
                "retired_employees": "退休人员",
                "pension_employees": "养老保险"
            }[table_type]
            print(f"   - {type_name}: {count} 个表")
        
        # 验证修复结果
        print(f"\n=== 验证差异化映射效果 ===")
        test_tables = {
            "salary_data_2027_04_active_employees": "全部在职人员",
            "salary_data_2027_04_a_grade_employees": "A岗职工",
            "salary_data_2027_04_retired_employees": "退休人员", 
            "salary_data_2027_04_pension_employees": "养老保险"
        }
        
        for table_name, table_desc in test_tables.items():
            if table_name in mappings["table_mappings"]:
                mapping = mappings["table_mappings"][table_name]["field_mappings"]
                print(f"\n{table_desc} ({table_name}):")
                
                # 显示关键字段的映射差异
                key_fields = ["basic_salary", "allowance", "total_salary", "department"]
                for field in key_fields:
                    if field in mapping:
                        print(f"  {field} -> {mapping[field]}")
            else:
                print(f"❌ 测试表 {table_name} 没有找到映射")
        
        print(f"\n=== 分页字段映射修复说明 ===")
        print("1. 已为所有表创建了差异化的字段映射配置")
        print("2. 不同类型的表现在有不同的字段显示名称：")
        print("   - 全部在职人员: basic_salary -> 基本工资")
        print("   - A岗职工: basic_salary -> 岗位工资") 
        print("   - 退休人员: basic_salary -> 退休金")
        print("   - 养老保险: basic_salary -> 缴费基数")
        print("3. 分页加载时会自动应用对应表的字段映射")
        print("4. 重启应用程序后生效")
        
    except Exception as e:
        logger.error(f"修复过程出错: {e}", exc_info=True)
        print(f"❌ 修复过程出错: {e}")

if __name__ == "__main__":
    main()
