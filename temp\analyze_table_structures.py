#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析不同类型工资表的字段结构差异
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.log_config import setup_logger

def main():
    """主函数"""
    logger = setup_logger(__name__)
    
    try:
        # 初始化表管理器
        table_manager = DynamicTableManager()
        
        # 测试不同类型的表
        test_tables = {
            "全部在职人员": "salary_data_2027_04_active_employees",
            "A岗职工": "salary_data_2027_04_a_grade_employees", 
            "退休人员": "salary_data_2027_04_retired_employees",
            "养老保险": "salary_data_2027_04_pension_employees"
        }
        
        print("=== 分析不同类型工资表的字段结构 ===\n")
        
        all_columns = {}
        
        for table_type, table_name in test_tables.items():
            print(f"=== {table_type} ({table_name}) ===")
            
            # 检查表是否存在
            if not table_manager.table_exists(table_name):
                print(f"❌ 表不存在，跳过")
                continue
            
            # 获取表字段
            columns_info = table_manager.get_table_columns(table_name)
            actual_columns = [col['name'] for col in columns_info]
            all_columns[table_type] = actual_columns
            
            print(f"字段数量: {len(actual_columns)}")
            print("字段列表:")
            for i, col in enumerate(actual_columns, 1):
                print(f"  {i:2d}. {col}")
            
            # 获取一条样本数据
            try:
                result = table_manager.get_dataframe_paginated(table_name, page=1, page_size=1)
                if isinstance(result, tuple):
                    df, total_count = result
                else:
                    df = result
                
                if df is not None and not df.empty:
                    print(f"样本数据 (总计{total_count if isinstance(result, tuple) else len(df)}条):")
                    sample_record = df.iloc[0].to_dict()
                    for key, value in sample_record.items():
                        value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                        print(f"  {key}: {value_str}")
                else:
                    print("❌ 无法获取样本数据")
            except Exception as e:
                print(f"❌ 获取样本数据失败: {e}")
            
            print()
        
        # 分析字段差异
        print("=== 字段差异分析 ===")
        
        if len(all_columns) > 1:
            # 找出所有字段的并集
            all_fields = set()
            for cols in all_columns.values():
                all_fields.update(cols)
            
            print(f"所有字段的并集 ({len(all_fields)} 个字段):")
            for field in sorted(all_fields):
                print(f"  - {field}")
            
            print("\n各表字段对比:")
            for table_type, cols in all_columns.items():
                print(f"\n{table_type}:")
                print(f"  字段数: {len(cols)}")
                
                # 该表独有的字段
                unique_fields = set(cols) - set().union(*[set(other_cols) for other_type, other_cols in all_columns.items() if other_type != table_type])
                if unique_fields:
                    print(f"  独有字段: {sorted(unique_fields)}")
                
                # 该表缺少的字段
                missing_fields = all_fields - set(cols)
                if missing_fields:
                    print(f"  缺少字段: {sorted(missing_fields)}")
        
        # 建议不同的字段映射策略
        print("\n=== 字段映射建议 ===")
        
        # 基础字段（所有表都有的）
        common_fields = set(all_columns[list(all_columns.keys())[0]])
        for cols in all_columns.values():
            common_fields &= set(cols)
        
        print(f"公共字段 ({len(common_fields)} 个): {sorted(common_fields)}")
        
        # 为每种表类型建议字段映射
        mapping_suggestions = {
            "全部在职人员": {
                "employee_id": "工号",
                "employee_name": "姓名", 
                "department": "部门",
                "position": "职位",
                "basic_salary": "基本工资",
                "performance_bonus": "绩效奖金",
                "overtime_pay": "加班费",
                "allowance": "津贴补贴",
                "deduction": "扣款",
                "total_salary": "应发合计"
            },
            "A岗职工": {
                "employee_id": "工号",
                "employee_name": "姓名",
                "department": "部门", 
                "position": "职位",
                "basic_salary": "基本工资",
                "performance_bonus": "绩效奖金",
                "overtime_pay": "加班费",
                "allowance": "津贴补贴",
                "deduction": "扣款",
                "total_salary": "应发合计"
            },
            "退休人员": {
                "employee_id": "工号",
                "employee_name": "姓名",
                "department": "原部门",
                "basic_salary": "退休金",
                "allowance": "补贴",
                "deduction": "扣款",
                "total_salary": "实发金额"
            },
            "养老保险": {
                "employee_id": "工号", 
                "employee_name": "姓名",
                "department": "部门",
                "basic_salary": "养老保险基数",
                "allowance": "补充保险",
                "deduction": "个人缴费",
                "total_salary": "单位缴费"
            }
        }
        
        print("\n建议的差异化字段映射:")
        for table_type, mapping in mapping_suggestions.items():
            print(f"\n{table_type}:")
            for db_field, display_name in mapping.items():
                print(f"  {db_field} -> {display_name}")
        
    except Exception as e:
        logger.error(f"分析过程出错: {e}", exc_info=True)
        print(f"❌ 分析过程出错: {e}")

if __name__ == "__main__":
    main()
