#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试界面刷新机制
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_import.header_edit_manager import HeaderEditManager
from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable

class TestRefreshWindow(QMainWindow):
    """测试界面刷新机制的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("界面刷新机制测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("准备测试界面刷新机制...")
        layout.addWidget(self.status_label)
        
        # 创建表格
        self.table = VirtualizedExpandableTable()
        layout.addWidget(self.table)
        
        # 测试按钮
        self.test_button = QPushButton("开始测试")
        self.test_button.clicked.connect(self.run_test)
        layout.addWidget(self.test_button)
        
        # 连接信号
        self.setup_signal_connections()
        
        # 初始化测试数据
        self.setup_test_data()
    
    def setup_signal_connections(self):
        """设置信号连接"""
        # 连接HeaderEditManager信号
        self.table.header_edit_manager.mapping_updated.connect(self.on_mapping_updated)
        self.table.header_edit_manager.edit_started.connect(self.on_edit_started)
        self.table.header_edit_manager.edit_completed.connect(self.on_edit_completed)
        self.table.header_edit_manager.edit_cancelled.connect(self.on_edit_cancelled)
        
        # 连接表格信号
        self.table.header_edited.connect(self.on_header_edited)
        self.table.field_mapping_updated.connect(self.on_field_mapping_updated)
    
    def setup_test_data(self):
        """设置测试数据"""
        # 设置表名
        self.table.current_table_name = "test_refresh_table"
        
        # 创建测试数据
        test_data = [
            {"employee_id": "001", "employee_name": "张三", "department": "技术部", "total_salary": 8000},
            {"employee_id": "002", "employee_name": "李四", "department": "财务部", "total_salary": 7500},
            {"employee_id": "003", "employee_name": "王五", "department": "人事部", "total_salary": 6500}
        ]
        
        headers = ["employee_id", "employee_name", "department", "total_salary"]
        
        # 应用模板映射
        config_manager = self.table.config_sync_manager
        success = config_manager.apply_template_to_table("test_refresh_table", "全部在职人员工资表")
        
        if success:
            self.status_label.setText("✅ 测试数据和映射配置已准备就绪")
        else:
            self.status_label.setText("⚠️ 映射配置失败，使用默认配置")
        
        # 设置表格数据
        self.table.set_data(test_data, headers, current_table_name="test_refresh_table")
    
    def run_test(self):
        """运行测试"""
        self.status_label.setText("🔄 测试进行中... 请双击任意表头进行编辑测试")
        self.test_button.setText("测试进行中...")
        self.test_button.setEnabled(False)
        
        # 5秒后重新启用按钮
        QTimer.singleShot(5000, self.reset_test_button)
    
    def reset_test_button(self):
        """重置测试按钮"""
        self.test_button.setText("开始测试")
        self.test_button.setEnabled(True)
    
    # 信号处理方法
    def on_mapping_updated(self, table_name: str, field_name: str, new_display_name: str):
        """映射更新信号处理"""
        self.status_label.setText(f"✅ 映射已更新: {field_name} -> {new_display_name}")
        print(f"映射更新: {table_name}.{field_name} -> {new_display_name}")
    
    def on_edit_started(self, table_name: str, field_name: str):
        """编辑开始信号处理"""
        self.status_label.setText(f"🔄 开始编辑: {field_name}")
        print(f"编辑开始: {table_name}.{field_name}")
    
    def on_edit_completed(self, table_name: str, old_name: str, new_name: str):
        """编辑完成信号处理"""
        self.status_label.setText(f"✅ 编辑完成: {old_name} -> {new_name}")
        print(f"编辑完成: {table_name} {old_name} -> {new_name}")
    
    def on_edit_cancelled(self, table_name: str, field_name: str):
        """编辑取消信号处理"""
        self.status_label.setText(f"❌ 编辑已取消: {field_name}")
        print(f"编辑取消: {table_name}.{field_name}")
    
    def on_header_edited(self, column: int, old_name: str, new_name: str):
        """表头编辑信号处理"""
        self.status_label.setText(f"🔄 表头已更新: 列{column} {old_name} -> {new_name}")
        print(f"表头编辑: 列{column} {old_name} -> {new_name}")
    
    def on_field_mapping_updated(self, table_name: str, mapping: dict):
        """字段映射更新信号处理"""
        self.status_label.setText(f"📋 字段映射已更新: {len(mapping)} 个字段")
        print(f"字段映射更新: {table_name} - {len(mapping)} 个字段")

def test_refresh_mechanism():
    """测试界面刷新机制"""
    print('=== 测试界面刷新机制 ===')
    
    app = QApplication([])
    
    # 创建测试窗口
    window = TestRefreshWindow()
    window.show()
    
    print('界面刷新机制测试窗口已启动')
    print('请双击表头进行编辑测试，观察界面是否实时刷新')
    
    # 运行应用
    app.exec_()

if __name__ == "__main__":
    test_refresh_mechanism()
