#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复字段映射问题的脚本
为所有工资数据表创建正确的字段映射配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.log_config import setup_logger

def create_standard_field_mapping():
    """创建标准字段映射"""
    return {
        # 基础信息字段
        "id": "ID",
        "employee_id": "工号", 
        "employee_name": "姓名",
        "id_card": "身份证号",
        "department": "部门",
        "position": "职位",
        
        # 工资字段
        "basic_salary": "基本工资",
        "performance_bonus": "绩效奖金", 
        "overtime_pay": "加班费",
        "allowance": "津贴补贴",
        "deduction": "扣款",
        "total_salary": "应发合计",
        
        # 时间字段
        "month": "月份",
        "year": "年份",
        "created_at": "创建时间",
        "updated_at": "更新时间"
    }

def main():
    """主函数"""
    logger = setup_logger(__name__)
    
    try:
        # 初始化表管理器
        table_manager = DynamicTableManager()
        
        # 获取所有工资数据表
        all_tables = table_manager.get_table_list(table_type='salary_data')
        salary_tables = [t['table_name'] for t in all_tables if isinstance(t, dict) and 'table_name' in t]
        
        print(f"发现 {len(salary_tables)} 个工资数据表")
        
        # 加载现有映射配置
        mapping_file = "state/data/field_mappings.json"
        if os.path.exists(mapping_file):
            with open(mapping_file, 'r', encoding='utf-8') as f:
                mappings = json.load(f)
        else:
            mappings = {
                "version": "2.0",
                "last_updated": "",
                "global_settings": {
                    "auto_generate_mappings": True,
                    "enable_smart_suggestions": True,
                    "save_edit_history": True,
                    "preserve_chinese_headers": True
                },
                "table_mappings": {},
                "field_templates": {},
                "user_preferences": {
                    "default_field_patterns": {},
                    "recent_edits": [],
                    "favorite_mappings": []
                }
            }
        
        # 标准字段映射
        standard_mapping = create_standard_field_mapping()
        
        # 为每个表创建映射
        updated_count = 0
        for table_name in salary_tables:
            print(f"\n处理表: {table_name}")
            
            # 检查表是否存在
            if not table_manager.table_exists(table_name):
                print(f"  ❌ 表不存在，跳过")
                continue
            
            # 获取表字段
            columns_info = table_manager.get_table_columns(table_name)
            if isinstance(columns_info[0], dict):
                # 如果返回的是字典格式（包含详细信息）
                actual_columns = [col['name'] for col in columns_info]
            else:
                # 如果返回的是简单列表
                actual_columns = columns_info
            
            print(f"  表字段: {actual_columns}")
            
            # 创建该表的字段映射
            table_mapping = {}
            for col in actual_columns:
                if col in standard_mapping:
                    table_mapping[col] = standard_mapping[col]
                else:
                    # 对于未知字段，保持原名
                    table_mapping[col] = col
            
            # 保存到映射配置中
            mappings["table_mappings"][table_name] = {
                "field_mappings": table_mapping,
                "original_excel_headers": {},
                "metadata": {
                    "source": "auto_fix",
                    "auto_generated": True,
                    "user_modified": False,
                    "created_at": "2025-06-27T14:40:00.000000",
                    "has_chinese_headers": True
                }
            }
            
            print(f"  ✅ 已创建字段映射: {len(table_mapping)} 个字段")
            updated_count += 1
        
        # 更新时间戳
        from datetime import datetime
        mappings["last_updated"] = datetime.now().isoformat()
        
        # 保存映射配置
        os.makedirs(os.path.dirname(mapping_file), exist_ok=True)
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mappings, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 修复完成！")
        print(f"   - 处理了 {updated_count} 个表")
        print(f"   - 配置文件已保存: {mapping_file}")
        
        # 验证修复结果
        print(f"\n=== 验证修复结果 ===")
        test_table = "salary_data_2027_03_a_grade_employees"
        if test_table in mappings["table_mappings"]:
            test_mapping = mappings["table_mappings"][test_table]["field_mappings"]
            print(f"表 {test_table} 的字段映射:")
            for db_field, display_name in test_mapping.items():
                print(f"  {db_field} -> {display_name}")
        else:
            print(f"❌ 测试表 {test_table} 没有找到映射")
        
    except Exception as e:
        logger.error(f"修复过程出错: {e}", exc_info=True)
        print(f"❌ 修复过程出错: {e}")

if __name__ == "__main__":
    main()
