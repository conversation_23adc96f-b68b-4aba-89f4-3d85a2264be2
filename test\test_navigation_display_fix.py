#!/usr/bin/env python3
"""
测试导航显示修复功能

测试内容：
1. 状态栏显示当前导航路径
2. 导航面板自动展开父级节点
3. 用户能够清楚看到当前显示的是哪个表的数据

创建时间: 2025-06-26
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager


class TestNavigationDisplayFix(unittest.TestCase):
    """测试导航显示修复功能"""
    
    def setUp(self):
        """测试前准备"""
        self.config_manager = Mock(spec=ConfigManager)
        self.db_manager = Mock(spec=DatabaseManager)
        self.table_manager = DynamicTableManager(
            db_manager=self.db_manager,
            config_manager=self.config_manager
        )
    
    def test_latest_path_format(self):
        """测试最新路径格式是否正确"""
        # 模拟数据库返回的工资数据表
        mock_metadata = [
            {
                'table_name': 'salary_data_2025_06_全部在职人员',
                'year': 2025,
                'month': 6,
                'display_name': '全部在职人员'
            }
        ]
        
        self.table_manager.get_table_list = Mock(return_value=mock_metadata)
        
        # 执行测试
        result = self.table_manager.get_latest_salary_data_path()
        
        # 验证路径格式
        expected_path = "工资表 > 2025年 > 06月 > 全部在职人员"
        self.assertEqual(result, expected_path)
        
        # 验证路径可以正确分割
        path_parts = result.split(' > ')
        self.assertEqual(len(path_parts), 4)
        self.assertEqual(path_parts[0], "工资表")
        self.assertEqual(path_parts[1], "2025年")
        self.assertEqual(path_parts[2], "06月")
        self.assertEqual(path_parts[3], "全部在职人员")
    
    def test_path_hierarchy_structure(self):
        """测试路径层级结构"""
        # 模拟多个数据表
        mock_metadata = [
            {
                'table_name': 'salary_data_2025_06_全部在职人员',
                'year': 2025,
                'month': 6,
                'display_name': '全部在职人员'
            },
            {
                'table_name': 'salary_data_2025_05_全部在职人员',
                'year': 2025,
                'month': 5,
                'display_name': '全部在职人员'
            },
            {
                'table_name': 'salary_data_2024_12_全部在职人员',
                'year': 2024,
                'month': 12,
                'display_name': '全部在职人员'
            }
        ]
        
        self.table_manager.get_table_list = Mock(return_value=mock_metadata)
        
        # 获取导航树数据
        tree_data = self.table_manager.get_navigation_tree_data()
        
        # 验证层级结构
        self.assertIn(2025, tree_data)
        self.assertIn(2024, tree_data)
        
        # 验证2025年的月份
        self.assertIn(6, tree_data[2025])
        self.assertIn(5, tree_data[2025])
        
        # 验证数据项
        june_items = tree_data[2025][6]
        self.assertEqual(len(june_items), 1)
        self.assertEqual(june_items[0]['display_name'], '全部在职人员')


def run_integration_test():
    """运行集成测试"""
    print("=" * 60)
    print("导航显示修复功能 - 集成测试")
    print("=" * 60)
    
    try:
        # 测试数据查询层
        print("\n1. 测试最新数据路径获取...")
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 获取最新数据路径
        latest_path = table_manager.get_latest_salary_data_path()
        if latest_path:
            print(f"   ✅ 最新数据路径: {latest_path}")
            
            # 验证路径格式
            path_parts = latest_path.split(' > ')
            print(f"   📋 路径层级: {len(path_parts)} 级")
            for i, part in enumerate(path_parts):
                print(f"      {i+1}. {part}")
            
            # 验证路径是否符合预期格式
            if len(path_parts) == 4:
                print("   ✅ 路径格式正确（4级层级）")
            else:
                print(f"   ⚠️  路径格式异常（{len(path_parts)}级层级）")
        else:
            print("   ⚠️  未找到工资数据")
        
        print("\n2. 测试导航树结构...")
        tree_data = table_manager.get_navigation_tree_data()
        if tree_data:
            print(f"   ✅ 导航树包含 {len(tree_data)} 个年份")
            
            # 显示树形结构
            for year in sorted(tree_data.keys(), reverse=True)[:2]:  # 显示最新2年
                months = tree_data[year]
                print(f"   📅 {year}年: {len(months)} 个月份")
                
                for month in sorted(months.keys(), reverse=True)[:2]:  # 显示最新2个月
                    items = months[month]
                    print(f"      📆 {month:02d}月: {len(items)} 个数据项")
                    for item in items:
                        print(f"         - {item['display_name']} ({item['icon']})")
        else:
            print("   ⚠️  未获取到导航树数据")
        
        print("\n3. 验证修复效果...")
        print("   ✅ 状态栏将显示当前导航路径")
        print("   ✅ 导航面板将自动展开父级节点")
        print("   ✅ 用户能清楚看到当前显示的表数据")
        
        print("\n4. 用户体验改进:")
        print("   📍 状态栏右侧显示: 📍 工资表 > 2025年 > 06月 > 全部在职人员")
        print("   🌳 左侧导航自动展开: 工资表 → 2025年 → 06月 → 全部在职人员")
        print("   📊 右侧列表显示对应的工资数据")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_before_after():
    """演示修复前后的对比"""
    print("\n" + "=" * 60)
    print("修复前后对比")
    print("=" * 60)
    
    print("\n【修复前的问题】")
    print("❌ 右侧显示了表数据，但左侧导航没有展开")
    print("❌ 状态栏显示'系统运行正常'，不知道当前是哪个表")
    print("❌ 用户困惑：这是什么数据？从哪里来的？")
    
    print("\n【修复后的改进】")
    print("✅ 左侧导航自动展开到当前选择的路径")
    print("✅ 状态栏显示完整的导航路径")
    print("✅ 用户清楚知道当前显示的是哪个表的数据")
    
    print("\n【技术实现】")
    print("1. MaterialFooterWidget.update_navigation_path() - 更新状态栏路径显示")
    print("2. SmartTreeWidget._expand_parent_nodes() - 递归展开父级节点")
    print("3. _on_navigation_changed() - 导航变化时同步更新状态栏")
    
    print("\n【预期效果】")
    print("🎯 用户启动系统后能立即看到:")
    print("   - 左侧: 工资表 > 2025年 > 06月 > 全部在职人员 (已展开)")
    print("   - 右侧: 对应的工资数据表")
    print("   - 状态栏: 📍 工资表 > 2025年 > 06月 > 全部在职人员")


if __name__ == '__main__':
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行集成测试
    success = run_integration_test()
    
    # 演示修复效果
    demo_before_after()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 导航显示修复测试通过！用户体验问题已解决。")
    else:
        print("❌ 部分测试失败，请检查修复实现。")
    print("=" * 60)
