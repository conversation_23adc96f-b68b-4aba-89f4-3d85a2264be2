#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据显示问题的脚本
检查数据库中的实际数据内容和字段映射
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.log_config import setup_logger
import json

def main():
    """主函数"""
    logger = setup_logger(__name__)
    
    try:
        # 初始化表管理器
        table_manager = DynamicTableManager()
        
        # 测试表名
        test_table = "salary_data_2027_03_a_grade_employees"
        
        print(f"=== 调试表: {test_table} ===")
        
        # 1. 检查表是否存在
        if not table_manager.table_exists(test_table):
            print(f"❌ 表 {test_table} 不存在")
            return
        
        print(f"✅ 表 {test_table} 存在")
        
        # 2. 获取表结构
        print("\n=== 表结构信息 ===")
        columns = table_manager.get_table_columns(test_table)
        print(f"表字段数量: {len(columns)}")
        print("表字段列表:")
        for i, col in enumerate(columns, 1):
            print(f"  {i:2d}. {col}")
        
        # 3. 获取前5条数据
        print("\n=== 前5条数据内容 ===")
        result = table_manager.get_dataframe_paginated(test_table, page=1, page_size=5)

        # get_dataframe_paginated 返回 (df, total_count) 元组
        if isinstance(result, tuple):
            df, total_count = result
            print(f"总记录数: {total_count}")
        else:
            df = result

        if df is None or df.empty:
            print("❌ 无法获取数据或数据为空")
            return
        
        print(f"获取到数据: {len(df)} 行 x {len(df.columns)} 列")
        print("\n数据预览:")
        print(df.head())
        
        # 4. 检查数据类型
        print("\n=== 数据类型信息 ===")
        print(df.dtypes)
        
        # 5. 检查是否有空值
        print("\n=== 空值检查 ===")
        null_counts = df.isnull().sum()
        print("各字段空值数量:")
        for col, count in null_counts.items():
            print(f"  {col}: {count}")
        
        # 6. 检查字段映射配置
        print("\n=== 字段映射配置 ===")
        mapping_file = "state/data/field_mappings.json"
        if os.path.exists(mapping_file):
            with open(mapping_file, 'r', encoding='utf-8') as f:
                mappings = json.load(f)
            
            if test_table in mappings:
                print(f"表 {test_table} 的字段映射:")
                for db_field, display_name in mappings[test_table].items():
                    print(f"  {db_field} -> {display_name}")
            else:
                print(f"❌ 表 {test_table} 没有字段映射配置")
        else:
            print(f"❌ 字段映射文件不存在: {mapping_file}")
        
        # 7. 转换为字典格式（模拟界面数据传递）
        print("\n=== 转换为界面数据格式 ===")
        data_records = df.to_dict('records')
        print(f"转换后记录数: {len(data_records)}")
        
        if data_records:
            print("第一条记录内容:")
            first_record = data_records[0]
            for key, value in first_record.items():
                print(f"  {key}: {value} ({type(value).__name__})")
        
        # 8. 检查是否有None值
        print("\n=== None值检查 ===")
        none_fields = []
        if data_records:
            for key, value in data_records[0].items():
                if value is None:
                    none_fields.append(key)
        
        if none_fields:
            print(f"❌ 发现None值字段: {none_fields}")
        else:
            print("✅ 没有发现None值字段")
        
        # 9. 检查数据库原始查询
        print("\n=== 数据库原始查询 ===")
        try:
            import sqlite3
            db_path = table_manager.db_path
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询前3条记录
            cursor.execute(f"SELECT * FROM {test_table} LIMIT 3")
            raw_data = cursor.fetchall()
            
            # 获取列名
            cursor.execute(f"PRAGMA table_info({test_table})")
            column_info = cursor.fetchall()
            column_names = [col[1] for col in column_info]
            
            print(f"数据库原始数据 (前3条):")
            for i, row in enumerate(raw_data):
                print(f"记录 {i+1}:")
                for j, value in enumerate(row):
                    col_name = column_names[j] if j < len(column_names) else f"col_{j}"
                    print(f"  {col_name}: {value} ({type(value).__name__})")
                print()
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 数据库原始查询失败: {e}")
        
    except Exception as e:
        logger.error(f"调试过程出错: {e}", exc_info=True)
        print(f"❌ 调试过程出错: {e}")

if __name__ == "__main__":
    main()
