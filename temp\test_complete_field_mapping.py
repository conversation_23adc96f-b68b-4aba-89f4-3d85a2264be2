#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段映射功能完整测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.data_import.header_edit_manager import HeaderEditManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_storage.database_manager import DatabaseManager
import pandas as pd

def test_complete_field_mapping():
    """完整的字段映射功能测试"""
    print('=== 字段映射功能完整测试 ===')
    
    test_results = {
        "config_initialization": False,
        "template_application": False,
        "excel_import_simulation": False,
        "field_mapping_application": False,
        "header_edit_simulation": False,
        "interface_refresh": False
    }
    
    try:
        # 1. 测试配置初始化
        print('\n1. 测试配置初始化')
        config_manager = ConfigSyncManager()
        
        # 检查配置文件是否存在
        config = config_manager._load_config_file()
        if config and config.get("version") == "2.0":
            print('✅ 配置文件初始化成功')
            test_results["config_initialization"] = True
        else:
            print('❌ 配置文件初始化失败')
        
        # 检查模板是否存在
        templates = config.get("field_templates", {})
        expected_templates = ["离休人员工资表", "退休人员工资表", "全部在职人员工资表", "A岗职工"]
        
        for template_name in expected_templates:
            template = config_manager.get_template_mapping(template_name)
            if template:
                print(f'✅ {template_name} 模板加载成功: {len(template)} 个字段')
            else:
                print(f'❌ {template_name} 模板加载失败')
        
        # 2. 测试模板应用
        print('\n2. 测试模板应用')
        test_table_name = "test_complete_mapping_table"
        
        success = config_manager.apply_template_to_table(test_table_name, "全部在职人员工资表")
        if success:
            print(f'✅ 模板应用成功: {test_table_name}')
            test_results["template_application"] = True
            
            # 验证映射
            mapping = config_manager.load_mapping(test_table_name)
            if mapping:
                print(f'✅ 映射验证成功: {len(mapping)} 个字段')
                print(f'   示例映射: {list(mapping.items())[:3]}')
            else:
                print('❌ 映射验证失败')
        else:
            print('❌ 模板应用失败')
        
        # 3. 模拟Excel导入流程
        print('\n3. 模拟Excel导入流程')
        
        # 创建必要的组件
        db_manager = DatabaseManager()
        table_manager = DynamicTableManager(db_manager)
        importer = MultiSheetImporter(table_manager)
        
        # 模拟Excel表头
        excel_headers = ["工号", "姓名", "部门名称", "2025年岗位工资", "2025年薪级工资", "津贴", "应发工资"]
        sheet_name = "全部在职人员工资表"
        
        # 测试表类型检测
        detected_type = importer._detect_table_type_from_headers(excel_headers, sheet_name)
        if detected_type == "全部在职人员工资表":
            print(f'✅ 表类型检测成功: {detected_type}')
            test_results["excel_import_simulation"] = True
        else:
            print(f'❌ 表类型检测失败: 期望 "全部在职人员工资表"，实际 {detected_type}')
        
        # 测试模板适配
        if detected_type:
            template = config_manager.get_template_mapping(detected_type)
            if template:
                adapted_mapping = importer._adapt_template_to_excel_headers(template, excel_headers)
                print(f'✅ 模板适配成功: {len(adapted_mapping)} 个字段')
                print(f'   适配示例: {list(adapted_mapping.items())[:3]}')
            else:
                print('❌ 模板获取失败')
        
        # 4. 测试字段映射应用
        print('\n4. 测试字段映射应用')
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'employee_id': ['001', '002', '003'],
            'employee_name': ['张三', '李四', '王五'],
            'department': ['技术部', '财务部', '人事部'],
            'position_salary_2025': [4000, 3500, 3000],
            'total_salary': [8000, 7500, 6500]
        })
        
        print(f'   原始数据表头: {list(test_data.columns)}')
        
        # 模拟主界面的字段映射应用
        def apply_field_mapping_to_dataframe(df: pd.DataFrame, table_name: str) -> pd.DataFrame:
            """模拟主界面的字段映射方法"""
            field_mapping = config_manager.load_mapping(table_name)
            if not field_mapping:
                return df
            
            column_rename_map = {}
            for db_field, display_name in field_mapping.items():
                if db_field in df.columns and display_name:
                    column_rename_map[db_field] = display_name
            
            if column_rename_map:
                return df.rename(columns=column_rename_map)
            return df
        
        mapped_data = apply_field_mapping_to_dataframe(test_data, test_table_name)
        print(f'   映射后数据表头: {list(mapped_data.columns)}')
        
        if list(mapped_data.columns) != list(test_data.columns):
            print('✅ 字段映射应用成功')
            test_results["field_mapping_application"] = True
        else:
            print('❌ 字段映射应用失败')
        
        # 5. 模拟表头编辑
        print('\n5. 模拟表头编辑')
        
        header_edit_manager = HeaderEditManager(config_manager)
        
        # 模拟更新映射
        old_display_name = "工号"
        new_display_name = "员工编号"
        
        success = config_manager.update_mapping(test_table_name, "employee_id", new_display_name)
        if success:
            print(f'✅ 表头编辑成功: {old_display_name} -> {new_display_name}')
            test_results["header_edit_simulation"] = True
            
            # 验证更新后的映射
            updated_mapping = config_manager.load_mapping(test_table_name)
            if updated_mapping and updated_mapping.get("employee_id") == new_display_name:
                print('✅ 映射更新验证成功')
            else:
                print('❌ 映射更新验证失败')
        else:
            print('❌ 表头编辑失败')
        
        # 6. 测试界面刷新（信号机制）
        print('\n6. 测试界面刷新机制')
        
        from PyQt5.QtCore import QCoreApplication
        app = QCoreApplication([])
        
        # 创建信号接收器
        class TestSignalReceiver:
            def __init__(self):
                self.signals_received = []
            
            def on_mapping_updated(self, table_name, field_name, new_display_name):
                self.signals_received.append(f"mapping_updated: {field_name} -> {new_display_name}")
        
        receiver = TestSignalReceiver()
        header_edit_manager.mapping_updated.connect(receiver.on_mapping_updated)
        
        # 发送测试信号
        header_edit_manager.mapping_updated.emit(test_table_name, "employee_name", "员工姓名")
        
        if receiver.signals_received:
            print(f'✅ 界面刷新机制正常: 接收到 {len(receiver.signals_received)} 个信号')
            test_results["interface_refresh"] = True
        else:
            print('❌ 界面刷新机制异常: 未接收到信号')
        
        # 7. 测试结果汇总
        print('\n=== 测试结果汇总 ===')
        
        total_tests = len(test_results)
        passed_tests = sum(test_results.values())
        
        print(f'总测试项目: {total_tests}')
        print(f'通过测试: {passed_tests}')
        print(f'成功率: {passed_tests/total_tests*100:.1f}%')
        
        print('\n详细结果:')
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f'  {test_name}: {status}')
        
        if passed_tests == total_tests:
            print('\n🎉 所有测试通过！字段映射功能完全正常。')
            return True
        else:
            print(f'\n⚠️ {total_tests - passed_tests} 项测试失败，需要进一步检查。')
            return False
        
    except Exception as e:
        print(f'❌ 测试过程中发生错误: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_complete_field_mapping()
    
    if success:
        print('\n✅ 字段映射问题已完全解决！')
        print('📋 解决方案总结:')
        print('  1. ✅ 创建了完整的字段映射配置文件')
        print('  2. ✅ 实现了4类工资表的模板支持')
        print('  3. ✅ 统一了字段映射格式和应用逻辑')
        print('  4. ✅ 添加了实时界面刷新机制')
        print('  5. ✅ 完善了表头双击编辑功能')
    else:
        print('\n⚠️ 字段映射功能仍有问题需要解决。')
