#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实际系统中的字段映射
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from src.gui.prototype.prototype_main_window import PrototypeMainWindow
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
import time

def test_actual_system_mapping():
    """测试实际系统中的字段映射"""
    print('=== 测试实际系统中的字段映射 ===')
    
    try:
        # 创建QApplication
        app = QApplication([])
        
        # 创建必要的组件
        print('\n1. 创建必要的组件')
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        table_manager = DynamicTableManager(db_manager)

        # 创建主窗口
        print('\n2. 创建主窗口')
        main_window = PrototypeMainWindow(config_manager, db_manager, table_manager)
        
        # 检查ConfigSyncManager
        print('\n3. 检查ConfigSyncManager')
        if hasattr(main_window, 'config_sync_manager'):
            config_manager = main_window.config_sync_manager
            print('✅ 主窗口有ConfigSyncManager')
            
            # 检查2027年4月的表映射
            april_tables = [
                "salary_data_2027_04_retired_employees",
                "salary_data_2027_04_pension_employees", 
                "salary_data_2027_04_active_employees",
                "salary_data_2027_04_a_grade_employees"
            ]
            
            for table_name in april_tables:
                mapping = config_manager.load_mapping(table_name)
                if mapping:
                    print(f'✅ {table_name}: {len(mapping)} 个字段映射')
                else:
                    print(f'❌ {table_name}: 没有字段映射')
        else:
            print('❌ 主窗口没有ConfigSyncManager')
        
        # 检查字段映射应用方法
        print('\n4. 检查字段映射应用方法')
        if hasattr(main_window, '_apply_field_mapping_to_dataframe'):
            print('✅ 主窗口有_apply_field_mapping_to_dataframe方法')
            
            # 测试方法
            import pandas as pd
            test_df = pd.DataFrame({
                'employee_id': ['001'],
                'employee_name': ['张三'],
                'department': ['技术部'],
                'total_salary': [8000]
            })
            
            test_table = "salary_data_2027_04_active_employees"
            mapped_df = main_window._apply_field_mapping_to_dataframe(test_df, test_table)
            
            print(f'   原始表头: {list(test_df.columns)}')
            print(f'   映射表头: {list(mapped_df.columns)}')
            
            if list(mapped_df.columns) != list(test_df.columns):
                print('✅ 字段映射方法工作正常')
            else:
                print('❌ 字段映射方法没有改变表头')
        else:
            print('❌ 主窗口没有_apply_field_mapping_to_dataframe方法')
        
        # 检查表格组件
        print('\n5. 检查表格组件')
        if hasattr(main_window, 'main_workspace') and hasattr(main_window.main_workspace, 'expandable_table'):
            table = main_window.main_workspace.expandable_table
            print('✅ 找到表格组件')
            
            # 检查表格组件的字段映射方法
            if hasattr(table, '_apply_field_mapping'):
                print('✅ 表格组件有_apply_field_mapping方法')
                
                # 测试表格组件的映射
                test_headers = ['employee_id', 'employee_name', 'department', 'total_salary']
                table.current_table_name = "salary_data_2027_04_active_employees"
                
                mapped_headers = table._apply_field_mapping(test_headers)
                print(f'   原始表头: {test_headers}')
                print(f'   映射表头: {mapped_headers}')
                
                if mapped_headers != test_headers:
                    print('✅ 表格组件字段映射工作正常')
                else:
                    print('❌ 表格组件字段映射没有改变表头')
            else:
                print('❌ 表格组件没有_apply_field_mapping方法')
        else:
            print('❌ 没有找到表格组件')
        
        # 模拟实际的数据加载流程
        print('\n6. 模拟实际数据加载流程')
        
        # 检查动态表管理器
        if hasattr(main_window, 'dynamic_table_manager'):
            table_manager = main_window.dynamic_table_manager
            test_table = "salary_data_2027_04_active_employees"
            
            if table_manager.table_exists(test_table):
                print(f'✅ 表 {test_table} 存在')
                
                # 获取原始数据
                df = table_manager.get_dataframe_from_table(test_table)
                if df is not None and not df.empty:
                    print(f'✅ 成功获取数据: {len(df)} 行, {len(df.columns)} 列')
                    print(f'   原始表头: {list(df.columns)[:5]}...')
                    
                    # 应用主窗口的字段映射
                    mapped_df = main_window._apply_field_mapping_to_dataframe(df, test_table)
                    print(f'   映射后表头: {list(mapped_df.columns)[:5]}...')
                    
                    # 检查是否真的改变了
                    if list(mapped_df.columns) != list(df.columns):
                        print('✅ 主窗口字段映射确实生效')
                        
                        # 模拟传递给表格组件
                        headers = mapped_df.columns.tolist()
                        data = mapped_df.to_dict('records')
                        
                        print(f'   传递给表格的表头: {headers[:5]}...')
                        
                        # 检查表格组件会如何处理这些已经映射过的表头
                        table = main_window.main_workspace.expandable_table
                        table.current_table_name = test_table
                        final_headers = table._apply_field_mapping(headers)
                        
                        print(f'   表格组件最终表头: {final_headers[:5]}...')
                        
                        if final_headers != headers:
                            print('⚠️ 表格组件又进行了一次映射，可能导致问题')
                        else:
                            print('✅ 表格组件保持了映射后的表头')
                    else:
                        print('❌ 主窗口字段映射没有生效')
                else:
                    print('❌ 无法获取表数据')
            else:
                print(f'❌ 表 {test_table} 不存在')
        else:
            print('❌ 没有找到动态表管理器')
        
        print('\n=== 测试完成 ===')
        
        # 不显示窗口，直接退出
        app.quit()
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_actual_system_mapping()
