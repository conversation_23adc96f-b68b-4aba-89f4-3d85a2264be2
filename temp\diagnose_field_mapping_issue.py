#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断字段映射问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_storage.database_manager import DatabaseManager
import pandas as pd

def diagnose_field_mapping_issue():
    """诊断字段映射问题"""
    print('=== 诊断字段映射问题 ===')
    
    try:
        # 1. 检查ConfigSyncManager
        print('\n1. 检查ConfigSyncManager')
        config_manager = ConfigSyncManager()
        
        # 检查配置文件加载
        config = config_manager._load_config_file()
        print(f'✅ 配置文件版本: {config.get("version")}')
        print(f'✅ 表映射数量: {len(config.get("table_mappings", {}))}')
        
        # 检查2027年4月的表映射
        table_mappings = config.get("table_mappings", {})
        april_2027_tables = [name for name in table_mappings.keys() if "2027_04" in name]
        print(f'✅ 2027年4月表数量: {len(april_2027_tables)}')
        
        for table_name in april_2027_tables:
            mapping = config_manager.load_mapping(table_name)
            if mapping:
                print(f'   - {table_name}: {len(mapping)} 个字段映射')
                # 显示前3个映射示例
                sample_mappings = list(mapping.items())[:3]
                for db_field, display_name in sample_mappings:
                    print(f'     {db_field} -> {display_name}')
            else:
                print(f'   - {table_name}: ❌ 映射加载失败')
        
        # 2. 检查数据库中的实际表
        print('\n2. 检查数据库中的实际表')
        db_manager = DatabaseManager()
        table_manager = DynamicTableManager(db_manager)
        
        # 获取所有表名
        all_tables_info = table_manager.get_table_list()
        all_table_names = [table['table_name'] for table in all_tables_info if isinstance(table, dict)]
        april_2027_db_tables = [name for name in all_table_names if "2027_04" in name]
        print(f'✅ 数据库中2027年4月表数量: {len(april_2027_db_tables)}')
        
        for table_name in april_2027_db_tables:
            try:
                df = table_manager.get_dataframe_from_table(table_name)
                if df is not None and not df.empty:
                    print(f'   - {table_name}: {len(df)} 行, {len(df.columns)} 列')
                    print(f'     原始列名: {list(df.columns)[:5]}...')
                else:
                    print(f'   - {table_name}: 空表或加载失败')
            except Exception as e:
                print(f'   - {table_name}: 加载错误 - {e}')
        
        # 3. 测试字段映射应用
        print('\n3. 测试字段映射应用')
        
        if april_2027_db_tables:
            test_table = april_2027_db_tables[0]
            print(f'测试表: {test_table}')
            
            # 获取原始数据
            df = table_manager.get_dataframe_from_table(test_table)
            if df is not None and not df.empty:
                print(f'原始表头: {list(df.columns)}')
                
                # 应用字段映射
                mapping = config_manager.load_mapping(test_table)
                if mapping:
                    print(f'可用映射: {len(mapping)} 个')
                    
                    # 模拟主界面的映射应用
                    column_rename_map = {}
                    for db_field, display_name in mapping.items():
                        if db_field in df.columns and display_name:
                            column_rename_map[db_field] = display_name
                    
                    if column_rename_map:
                        df_mapped = df.rename(columns=column_rename_map)
                        print(f'映射后表头: {list(df_mapped.columns)}')
                        print(f'✅ 成功应用 {len(column_rename_map)} 个字段映射')
                        
                        # 检查是否真的改变了
                        if list(df_mapped.columns) != list(df.columns):
                            print('✅ 字段映射确实生效了')
                        else:
                            print('❌ 字段映射没有改变表头')
                    else:
                        print('❌ 没有找到可应用的字段映射')
                else:
                    print('❌ 没有找到字段映射配置')
            else:
                print('❌ 无法加载表数据')
        
        # 4. 检查主界面是否正确应用映射
        print('\n4. 检查主界面映射应用逻辑')
        
        # 检查prototype_main_window中的映射应用方法
        try:
            from src.gui.prototype.prototype_main_window import PrototypeMainWindow
            print('✅ 主界面类导入成功')
            
            # 检查是否有_apply_field_mapping_to_dataframe方法
            if hasattr(PrototypeMainWindow, '_apply_field_mapping_to_dataframe'):
                print('✅ _apply_field_mapping_to_dataframe 方法存在')
            else:
                print('❌ _apply_field_mapping_to_dataframe 方法不存在')
                
        except Exception as e:
            print(f'❌ 主界面类导入失败: {e}')
        
        # 5. 检查VirtualizedExpandableTable的映射逻辑
        print('\n5. 检查表格组件映射逻辑')
        
        try:
            from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
            print('✅ 表格组件类导入成功')
            
            # 检查是否有_apply_field_mapping方法
            if hasattr(VirtualizedExpandableTable, '_apply_field_mapping'):
                print('✅ _apply_field_mapping 方法存在')
            else:
                print('❌ _apply_field_mapping 方法不存在')
                
        except Exception as e:
            print(f'❌ 表格组件类导入失败: {e}')
        
        print('\n=== 诊断完成 ===')
        
    except Exception as e:
        print(f'❌ 诊断过程中发生错误: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_field_mapping_issue()
