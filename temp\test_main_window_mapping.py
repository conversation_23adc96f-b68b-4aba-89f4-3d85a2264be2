#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主界面的字段映射功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_storage.database_manager import DatabaseManager
import pandas as pd

def test_main_window_mapping():
    """测试主界面的字段映射功能"""
    print('=== 测试主界面字段映射功能 ===')
    
    try:
        # 1. 创建必要的组件
        print('\n1. 初始化组件')
        db_manager = DatabaseManager()
        table_manager = DynamicTableManager(db_manager)
        config_manager = ConfigSyncManager()
        
        # 2. 模拟主界面的字段映射方法
        def apply_field_mapping_to_dataframe(df: pd.DataFrame, table_name: str) -> pd.DataFrame:
            """模拟主界面的字段映射方法"""
            try:
                # 获取字段映射配置
                field_mapping = config_manager.load_mapping(table_name)

                if not field_mapping:
                    print(f"   表 {table_name} 没有字段映射配置")
                    return df

                # 创建列名重命名映射：数据库字段名 → 用户显示名
                column_rename_map = {}

                for db_field, display_name in field_mapping.items():
                    if db_field in df.columns and display_name:
                        column_rename_map[db_field] = display_name

                # 应用重命名
                if column_rename_map:
                    df_renamed = df.rename(columns=column_rename_map)
                    print(f"   字段映射应用成功: {len(column_rename_map)} 个字段重命名")
                    print(f"   映射详情: {column_rename_map}")
                    return df_renamed
                else:
                    print(f"   表 {table_name} 无需字段重命名")
                    return df

            except Exception as e:
                print(f"   应用字段映射失败: {e}")
                return df
        
        # 3. 测试字段映射应用
        print('\n2. 测试字段映射应用')
        
        # 创建测试数据（模拟数据库中的数据）
        test_data = pd.DataFrame({
            'employee_id': ['001', '002', '003'],
            'employee_name': ['张三', '李四', '王五'],
            'department': ['技术部', '财务部', '人事部'],
            'total_salary': [8000, 7500, 6500]
        })
        
        test_table_name = "salary_data_2025_01_active_employees_test"
        
        print(f'   原始数据表头: {list(test_data.columns)}')
        
        # 应用字段映射
        mapped_data = apply_field_mapping_to_dataframe(test_data, test_table_name)
        
        print(f'   映射后数据表头: {list(mapped_data.columns)}')
        
        # 验证映射是否成功
        if list(mapped_data.columns) != list(test_data.columns):
            print('   ✅ 字段映射成功应用')
        else:
            print('   ⚠️ 字段映射未改变（可能没有配置）')
        
        # 4. 测试不同表类型的映射
        print('\n3. 测试不同表类型的映射')
        
        # 为不同表类型应用模板
        table_types = [
            ("salary_data_2025_01_retired_test", "退休人员工资表"),
            ("salary_data_2025_01_active_test", "全部在职人员工资表"),
            ("salary_data_2025_01_a_grade_test", "A岗职工")
        ]
        
        for table_name, table_type in table_types:
            print(f'\n   测试 {table_type}:')
            
            # 应用模板
            success = config_manager.apply_template_to_table(table_name, table_type)
            if success:
                print(f'     ✅ 模板应用成功')
                
                # 创建对应的测试数据
                if table_type == "退休人员工资表":
                    test_df = pd.DataFrame({
                        'employee_id': ['R001'],
                        'employee_name': ['退休张三'],
                        'department': ['原技术部'],
                        'basic_retirement_salary': [3000],
                        'total_salary': [3500]
                    })
                elif table_type == "全部在职人员工资表":
                    test_df = pd.DataFrame({
                        'employee_id': ['A001'],
                        'employee_name': ['在职李四'],
                        'department': ['技术部'],
                        'position_salary_2025': [4000],
                        'total_salary': [8000]
                    })
                else:  # A岗职工
                    test_df = pd.DataFrame({
                        'employee_id': ['G001'],
                        'employee_name': ['A岗王五'],
                        'department': ['教学部'],
                        'seniority_salary_2025': [2000],
                        'total_salary': [7000]
                    })
                
                # 应用映射
                mapped_df = apply_field_mapping_to_dataframe(test_df, table_name)
                
                print(f'     原始表头: {list(test_df.columns)}')
                print(f'     映射表头: {list(mapped_df.columns)}')
                
                if list(mapped_df.columns) != list(test_df.columns):
                    print(f'     ✅ {table_type} 字段映射成功')
                else:
                    print(f'     ⚠️ {table_type} 字段映射未改变')
            else:
                print(f'     ❌ 模板应用失败')
        
        print('\n=== 测试完成 ===')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_main_window_mapping()
