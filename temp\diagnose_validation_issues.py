#!/usr/bin/env python3
"""
数据导入验证问题诊断脚本

用于分析和诊断数据导入时出现的验证问题，特别是多Sheet导入时的验证错误。
"""

import sys
import os
import json
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到系统路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.log_config import setup_logger
from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.data_import.excel_importer import ExcelImporter
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager


def diagnose_validation_issues():
    """诊断验证问题的主函数"""
    logger = setup_logger(__name__)
    logger.info("开始诊断数据导入验证问题...")
    
    try:
        # 1. 初始化组件
        config_manager = ConfigManager()
        db_manager = config_manager.db_manager
        table_manager = DynamicTableManager(db_manager)
        
        # 2. 测试Excel文件路径
        test_file = Path("data/工资表/2025年5月份正式工工资（报财务)  最终版.xls")
        
        if not test_file.exists():
            logger.error(f"测试文件不存在: {test_file}")
            return
        
        logger.info(f"分析文件: {test_file}")
        
        # 3. 初始化导入器
        excel_importer = ExcelImporter()
        multi_sheet_importer = MultiSheetImporter(table_manager)
        
        # 4. 获取Sheet信息
        sheet_names = excel_importer.get_sheet_names(test_file)
        logger.info(f"检测到 {len(sheet_names)} 个工作表: {sheet_names}")
        
        # 5. 逐Sheet分析
        validation_report = {
            'file_path': str(test_file),
            'total_sheets': len(sheet_names),
            'sheet_analysis': {}
        }
        
        for sheet_name in sheet_names:
            logger.info(f"\n分析工作表: {sheet_name}")
            sheet_analysis = analyze_single_sheet(excel_importer, multi_sheet_importer, test_file, sheet_name, logger)
            validation_report['sheet_analysis'][sheet_name] = sheet_analysis
        
        # 6. 生成报告
        generate_validation_report(validation_report, logger)
        
        logger.info("验证问题诊断完成")
        
    except Exception as e:
        logger.error(f"诊断过程出错: {e}")
        import traceback
        traceback.print_exc()


def analyze_single_sheet(excel_importer: ExcelImporter, 
                        multi_sheet_importer: MultiSheetImporter,
                        file_path: Path, 
                        sheet_name: str, 
                        logger) -> Dict[str, Any]:
    """分析单个工作表的验证问题"""
    
    sheet_analysis = {
        'sheet_name': sheet_name,
        'data_info': {},
        'validation_issues': [],
        'suggestions': []
    }
    
    try:
        # 1. 读取Sheet数据
        df = excel_importer.import_data(file_path, sheet_name=sheet_name)
        
        sheet_analysis['data_info'] = {
            'total_rows': len(df),
            'total_columns': len(df.columns),
            'column_names': list(df.columns),
            'empty_rows': df.isnull().all(axis=1).sum(),
            'sample_data': df.head(3).to_dict() if len(df) > 0 else {}
        }
        
        logger.info(f"  数据规模: {len(df)} 行 x {len(df.columns)} 列")
        logger.info(f"  列名: {list(df.columns)}")
        
        # 2. 生成智能默认配置
        sheet_config = multi_sheet_importer._generate_smart_default_config(sheet_name, df)
        logger.info(f"  生成的配置: {json.dumps(sheet_config, ensure_ascii=False, indent=2)}")
        
        # 3. 执行验证
        validation_errors = multi_sheet_importer._validate_sheet_data(df, sheet_config)
        sheet_analysis['validation_issues'] = validation_errors
        
        if validation_errors:
            logger.warning(f"  验证问题 ({len(validation_errors)} 个):")
            for i, error in enumerate(validation_errors, 1):
                logger.warning(f"    {i}. {error}")
        else:
            logger.info("  ✓ 无验证问题")
        
        # 4. 生成改进建议
        suggestions = generate_sheet_suggestions(df, sheet_config, validation_errors)
        sheet_analysis['suggestions'] = suggestions
        
        if suggestions:
            logger.info(f"  改进建议:")
            for i, suggestion in enumerate(suggestions, 1):
                logger.info(f"    {i}. {suggestion}")
        
    except Exception as e:
        logger.error(f"  分析工作表 {sheet_name} 失败: {e}")
        sheet_analysis['error'] = str(e)
    
    return sheet_analysis


def generate_sheet_suggestions(df, sheet_config: Dict[str, Any], validation_errors: List[str]) -> List[str]:
    """为工作表生成改进建议"""
    suggestions = []
    
    required_fields = sheet_config.get('required_fields', [])
    available_columns = list(df.columns)
    
    # 分析字段映射建议
    field_mappings = {
        'employee_id': ['工号', '员工号', '职工编号', '编号'],
        'employee_name': ['姓名', '员工姓名', '职工姓名', '名字']
    }
    
    for required_field in required_fields:
        if required_field not in available_columns:
            if required_field in field_mappings:
                for synonym in field_mappings[required_field]:
                    for col in available_columns:
                        if synonym in str(col):
                            suggestions.append(f"建议映射 '{required_field}' → '{col}'")
                            break
    
    # 数据质量建议
    if len(df) == 0:
        suggestions.append("工作表为空，请检查数据源")
    elif len(df) < 5:
        suggestions.append("数据量较少，请确认是否为有效的工资数据")
    
    # 空值建议
    for col in df.columns:
        null_count = df[col].isnull().sum()
        if null_count > 0:
            null_percentage = (null_count / len(df)) * 100
            if null_percentage > 50:
                suggestions.append(f"列 '{col}' 空值过多 ({null_percentage:.1f}%)，建议检查数据质量")
    
    return suggestions


def generate_validation_report(report: Dict[str, Any], logger):
    """生成验证报告文件"""
    try:
        # 保存详细报告
        report_file = Path("temp/validation_diagnosis_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"详细报告已保存到: {report_file}")
        
        # 生成摘要
        total_issues = sum(len(sheet['validation_issues']) for sheet in report['sheet_analysis'].values())
        logger.info(f"\n📊 验证问题摘要:")
        logger.info(f"   总工作表数: {report['total_sheets']}")
        logger.info(f"   总验证问题: {total_issues}")
        
        for sheet_name, analysis in report['sheet_analysis'].items():
            issue_count = len(analysis.get('validation_issues', []))
            logger.info(f"   {sheet_name}: {issue_count} 个问题")
        
        # 生成用户友好的摘要
        summary_file = Path("temp/validation_summary.md")
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("# 数据导入验证问题分析报告\n\n")
            f.write(f"**文件:** {report['file_path']}\n")
            f.write(f"**分析时间:** {Path(__file__).stat().st_mtime}\n")
            f.write(f"**工作表数量:** {report['total_sheets']}\n")
            f.write(f"**总验证问题数:** {total_issues}\n\n")
            
            f.write("## 各工作表分析\n\n")
            for sheet_name, analysis in report['sheet_analysis'].items():
                f.write(f"### {sheet_name}\n\n")
                
                data_info = analysis.get('data_info', {})
                f.write(f"- **数据规模:** {data_info.get('total_rows', 0)} 行 x {data_info.get('total_columns', 0)} 列\n")
                f.write(f"- **列名:** {', '.join(data_info.get('column_names', []))}\n")
                
                issues = analysis.get('validation_issues', [])
                if issues:
                    f.write(f"- **验证问题 ({len(issues)} 个):**\n")
                    for issue in issues:
                        f.write(f"  - {issue}\n")
                else:
                    f.write("- **验证问题:** 无\n")
                
                suggestions = analysis.get('suggestions', [])
                if suggestions:
                    f.write(f"- **改进建议:**\n")
                    for suggestion in suggestions:
                        f.write(f"  - {suggestion}\n")
                
                f.write("\n")
        
        logger.info(f"用户友好摘要已保存到: {summary_file}")
        
    except Exception as e:
        logger.error(f"生成报告失败: {e}")


if __name__ == "__main__":
    diagnose_validation_issues() 