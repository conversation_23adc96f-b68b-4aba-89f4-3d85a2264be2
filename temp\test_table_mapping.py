#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试VirtualizedExpandableTable的字段映射功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable
from PyQt5.QtWidgets import QApplication

def test_table_mapping():
    """测试表格的字段映射功能"""
    print('=== 测试VirtualizedExpandableTable字段映射功能 ===')
    
    try:
        # 创建QApplication（GUI组件需要）
        app = QApplication([])
        
        # 1. 创建表格组件
        table = VirtualizedExpandableTable()
        table.current_table_name = "salary_data_2025_01_active_employees_test"
        
        # 2. 测试字段映射应用
        print('\n1. 测试字段映射应用')
        
        # 测试不同类型的表头
        test_headers = [
            ["employee_id", "employee_name", "department", "total_salary"],  # 数据库字段名
            ["工号", "姓名", "部门名称", "应发工资"],  # Excel列名
            ["序号", "人员代码", "姓名", "部门名称", "基本退休费", "津贴", "应发工资"]  # 混合格式
        ]
        
        for i, headers in enumerate(test_headers):
            print(f'\n   测试用例 {i+1}: {headers}')
            mapped_headers = table._apply_field_mapping(headers)
            print(f'   映射结果: {mapped_headers}')
            
            # 验证映射是否成功
            if mapped_headers != headers:
                print(f'   ✅ 字段映射已应用')
            else:
                print(f'   ⚠️ 字段映射未改变（可能没有配置或已经是显示名）')
        
        # 3. 测试Excel列名到数据库字段名的转换
        print('\n2. 测试Excel列名转换')
        test_excel_columns = [
            "工号", "姓名", "部门名称", "2025年岗位工资", "2025年薪级工资", 
            "津贴", "应发工资", "基本退休费", "护理费"
        ]
        
        for excel_col in test_excel_columns:
            db_field = table._excel_column_to_db_field(excel_col)
            print(f'   {excel_col} -> {db_field}')
        
        # 4. 测试字段名标准化
        print('\n3. 测试字段名标准化')
        test_field_names = [
            "2025年岗位工资", "员工#姓名", "部门@名称", "工资*合计"
        ]
        
        for field_name in test_field_names:
            normalized = table._normalize_field_name(field_name)
            print(f'   {field_name} -> {normalized}')
        
        # 5. 测试完整的数据设置流程
        print('\n4. 测试完整数据设置流程')
        test_data = [
            {"employee_id": "001", "employee_name": "张三", "department": "技术部", "total_salary": 8000},
            {"employee_id": "002", "employee_name": "李四", "department": "财务部", "total_salary": 7500}
        ]
        test_headers_for_data = ["employee_id", "employee_name", "department", "total_salary"]
        
        # 设置数据（这会触发字段映射）
        table.set_data(test_data, test_headers_for_data, current_table_name="salary_data_2025_01_active_employees_test")
        
        # 检查表头是否正确设置
        actual_headers = []
        for i in range(table.columnCount()):
            header_item = table.horizontalHeaderItem(i)
            if header_item:
                actual_headers.append(header_item.text())
        
        print(f'   原始表头: {test_headers_for_data}')
        print(f'   实际表头: {actual_headers}')
        
        if actual_headers != test_headers_for_data:
            print(f'   ✅ 字段映射在数据设置时已应用')
        else:
            print(f'   ⚠️ 字段映射未应用或表头未改变')
        
        print('\n=== 测试完成 ===')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_table_mapping()
