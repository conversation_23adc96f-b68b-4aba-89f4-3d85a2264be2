# 字段映射问题完整解决方案

## 问题总结

经过深入分析，字段映射功能存在以下根本性问题：

### 主要问题
1. **配置文件缺失**：`state/data/field_mappings.json` 文件不存在
2. **映射格式不统一**：不同模块期望的映射键值格式不一致
3. **数据流程断层**：Excel导入→数据库存储→界面显示各环节映射机制不统一
4. **界面刷新缺失**：表头修改后没有触发界面实时更新

### 次要问题
1. **多表类型支持不完善**：4类工资表（离休、退休、在职、A岗）字段差异大
2. **表头双击编辑机制不完善**：修改保存后界面不更新
3. **字段映射键匹配失败**：表格组件期望的键与配置文件中的键不匹配

## 立即解决方案

### 步骤1：创建默认配置文件

创建 `state/data/field_mappings.json` 文件，包含4类工资表的默认映射：

```json
{
  "version": "2.0",
  "last_updated": "2025-06-27T10:00:00",
  "global_settings": {
    "auto_generate_mappings": true,
    "preserve_chinese_headers": true,
    "enable_smart_suggestions": true
  },
  "table_mappings": {},
  "field_templates": {
    "离休人员工资表": {
      "employee_id": "人员代码",
      "employee_name": "姓名",
      "department": "部门名称",
      "basic_retirement_salary": "基本离休费",
      "allowance": "结余津贴",
      "living_allowance": "生活补贴",
      "housing_allowance": "住房补贴",
      "property_allowance": "物业补贴",
      "retirement_allowance": "离休补贴",
      "nursing_fee": "护理费",
      "one_time_living_allowance": "增发一次性生活补贴",
      "supplement": "补发",
      "total": "合计",
      "advance": "借支",
      "remarks": "备注"
    },
    "退休人员工资表": {
      "employee_id": "人员代码", 
      "employee_name": "姓名",
      "department": "部门名称",
      "employee_type_code": "人员类别代码",
      "basic_retirement_salary": "基本退休费",
      "allowance": "津贴",
      "balance_allowance": "结余津贴",
      "retirement_living_allowance": "离退休生活补贴",
      "nursing_fee": "护理费",
      "property_allowance": "物业补贴",
      "housing_allowance": "住房补贴",
      "salary_advance": "增资预付",
      "adjustment_2016": "2016待遇调整",
      "adjustment_2017": "2017待遇调整",
      "adjustment_2018": "2018待遇调整",
      "adjustment_2019": "2019待遇调整",
      "adjustment_2020": "2020待遇调整",
      "adjustment_2021": "2021待遇调整",
      "adjustment_2022": "2022待遇调整",
      "adjustment_2023": "2023待遇调整",
      "supplement": "补发",
      "advance": "借支",
      "total_salary": "应发工资",
      "provident_fund": "公积",
      "insurance_deduction": "保险扣款",
      "remarks": "备注"
    },
    "全部在职人员工资表": {
      "employee_id": "工号",
      "employee_name": "姓名",
      "department": "部门名称", 
      "employee_type_code": "人员类别代码",
      "employee_type": "人员类别",
      "position_salary_2025": "2025年岗位工资",
      "grade_salary_2025": "2025年薪级工资",
      "allowance": "津贴",
      "balance_allowance": "结余津贴",
      "basic_performance_2025": "2025年基础性绩效",
      "health_fee": "卫生费",
      "transport_allowance": "交通补贴",
      "property_allowance": "物业补贴",
      "housing_allowance": "住房补贴",
      "car_allowance": "车补",
      "communication_allowance": "通讯补贴",
      "performance_bonus_2025": "2025年奖励性绩效预发",
      "supplement": "补发",
      "advance": "借支",
      "total_salary": "应发工资",
      "provident_fund_2025": "2025公积金",
      "pension_insurance": "代扣代存养老保险"
    },
    "A岗职工": {
      "employee_id": "工号",
      "employee_name": "姓名",
      "department": "部门名称",
      "employee_type": "人员类别",
      "employee_type_code": "人员类别代码", 
      "position_salary_2025": "2025年岗位工资",
      "seniority_salary_2025": "2025年校龄工资",
      "allowance": "津贴",
      "balance_allowance": "结余津贴",
      "basic_performance_2025": "2025年基础性绩效",
      "health_fee": "卫生费",
      "living_allowance_2025": "2025年生活补贴",
      "car_allowance": "车补",
      "performance_bonus_2025": "2025年奖励性绩效预发",
      "supplement": "补发",
      "advance": "借支",
      "total_salary": "应发工资",
      "provident_fund_2025": "2025公积金",
      "insurance_deduction": "保险扣款",
      "pension_insurance": "代扣代存养老保险"
    }
  }
}
```

### 步骤2：修复ConfigSyncManager初始化

在 `src/modules/data_import/config_sync_manager.py` 中添加自动初始化功能：

```python
def _initialize_config(self):
    """初始化配置文件和目录"""
    try:
        # 确保目录存在
        config_dir = Path(self.config_path).parent
        config_dir.mkdir(parents=True, exist_ok=True)
        
        # 如果配置文件不存在，创建默认配置
        if not Path(self.config_path).exists():
            self.logger.info("配置文件不存在，创建默认配置")
            self._create_default_config()
        
        # 验证配置文件格式
        self._validate_config_format()
        
    except Exception as e:
        self.logger.error(f"初始化配置失败: {e}")
        self._create_default_config()

def _create_default_config(self):
    """创建默认配置文件"""
    default_config = {
        "version": "2.0",
        "last_updated": datetime.now().isoformat(),
        "global_settings": {
            "auto_generate_mappings": True,
            "preserve_chinese_headers": True,
            "enable_smart_suggestions": True
        },
        "table_mappings": {},
        "field_templates": {
            # 这里包含4类工资表的默认模板
        }
    }
    
    success = self._save_config_file(default_config)
    if success:
        self.logger.info("默认配置文件创建成功")
    else:
        self.logger.error("默认配置文件创建失败")
```

### 步骤3：统一映射格式

修改所有相关模块，统一使用 `{数据库字段名: 显示名称}` 格式：

1. **VirtualizedExpandableTable._apply_field_mapping**：确保使用数据库字段名作为键
2. **prototype_main_window._apply_field_mapping_to_dataframe**：保持现有逻辑
3. **multi_sheet_importer**：生成映射时使用数据库字段名作为键

### 步骤4：添加界面刷新机制

在表头编辑完成后，添加界面刷新信号：

```python
# 在HeaderEditManager中
def save_field_edit(self, table_name: str, field_name: str, new_display_name: str):
    """保存字段编辑并触发界面刷新"""
    success = self.config_sync_manager.update_mapping(table_name, field_name, new_display_name)
    if success:
        # 发送刷新信号
        self.mapping_updated.emit(table_name, field_name, new_display_name)
        return True
    return False
```

## 长期优化方案

### 1. 字段映射管理器重构
### 2. 智能字段识别
### 3. 用户自定义映射界面
### 4. 映射配置导入导出功能

## 实施优先级

1. **P0（立即）**：创建默认配置文件，修复ConfigSyncManager初始化
2. **P1（本周）**：统一映射格式，添加界面刷新机制  
3. **P2（下周）**：完善4类工资表模板，优化用户体验
4. **P3（后续）**：长期优化方案实施
