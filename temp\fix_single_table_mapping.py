#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复单个表的字段映射问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.utils.log_config import setup_logger

def create_standard_field_mapping():
    """创建标准字段映射"""
    return {
        # 基础信息字段
        "id": "ID",
        "employee_id": "工号", 
        "employee_name": "姓名",
        "id_card": "身份证号",
        "department": "部门",
        "position": "职位",
        
        # 工资字段
        "basic_salary": "基本工资",
        "performance_bonus": "绩效奖金", 
        "overtime_pay": "加班费",
        "allowance": "津贴补贴",
        "deduction": "扣款",
        "total_salary": "应发合计",
        
        # 时间字段
        "month": "月份",
        "year": "年份",
        "created_at": "创建时间",
        "updated_at": "更新时间"
    }

def main():
    """主函数"""
    logger = setup_logger(__name__)
    
    try:
        # 初始化表管理器
        table_manager = DynamicTableManager()
        
        # 测试表名列表
        test_tables = [
            "salary_data_2027_03_a_grade_employees",
            "salary_data_2027_02_active_employees",
            "salary_data_2027_02_a_grade_employees"
        ]
        
        # 加载现有映射配置
        mapping_file = "state/data/field_mappings.json"
        if os.path.exists(mapping_file):
            with open(mapping_file, 'r', encoding='utf-8') as f:
                mappings = json.load(f)
        else:
            print(f"❌ 映射配置文件不存在: {mapping_file}")
            return

        # 标准字段映射
        standard_mapping = create_standard_field_mapping()

        # 处理每个表
        updated_count = 0
        for test_table in test_tables:
            print(f"\n=== 修复表: {test_table} ===")

            # 检查表是否存在
            if not table_manager.table_exists(test_table):
                print(f"❌ 表 {test_table} 不存在，跳过")
                continue

            # 获取表字段
            columns_info = table_manager.get_table_columns(test_table)
            actual_columns = [col['name'] for col in columns_info]

            print(f"表字段: {actual_columns}")

            # 创建该表的字段映射
            table_mapping = {}
            for col in actual_columns:
                if col in standard_mapping:
                    table_mapping[col] = standard_mapping[col]
                else:
                    # 对于未知字段，保持原名
                    table_mapping[col] = col

            # 保存到映射配置中
            mappings["table_mappings"][test_table] = {
                "field_mappings": table_mapping,
                "original_excel_headers": {},
                "metadata": {
                    "source": "auto_fix",
                    "auto_generated": True,
                    "user_modified": False,
                    "created_at": "2025-06-27T14:40:00.000000",
                    "has_chinese_headers": True
                }
            }

            print(f"✅ 表 {test_table} 修复完成！")
            print(f"   - 字段映射: {len(table_mapping)} 个字段")
            updated_count += 1

        # 更新时间戳
        from datetime import datetime
        mappings["last_updated"] = datetime.now().isoformat()

        # 保存映射配置
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(mappings, f, ensure_ascii=False, indent=2)

        print(f"\n✅ 全部修复完成！")
        print(f"   - 处理了 {updated_count} 个表")
        print(f"   - 配置文件已保存: {mapping_file}")

        # 验证修复结果
        print(f"\n=== 验证修复结果 ===")
        with open(mapping_file, 'r', encoding='utf-8') as f:
            verify_mappings = json.load(f)

        for test_table in test_tables:
            if test_table in verify_mappings["table_mappings"]:
                print(f"✅ 配置文件中找到表 {test_table}")
                verify_mapping = verify_mappings["table_mappings"][test_table]["field_mappings"]
                print(f"   映射字段数: {len(verify_mapping)}")
            else:
                print(f"❌ 配置文件中没有找到表 {test_table}")
        
    except Exception as e:
        logger.error(f"修复过程出错: {e}", exc_info=True)
        print(f"❌ 修复过程出错: {e}")

if __name__ == "__main__":
    main()
