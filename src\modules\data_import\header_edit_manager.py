"""
表头编辑管理器

功能说明:
- 处理表头双击编辑的完整交互流程
- 编辑界面、输入验证、冲突检测
- 与现有表格组件无缝集成
- 实时保存和界面刷新

功能函数:
- show_edit_dialog(): 显示字段编辑对话框
- validate_field_name(): 验证字段名称
- update_field_mapping(): 更新字段映射
- generate_suggestions(): 生成智能建议

创建时间: 2025-06-24
作者: 开发团队
"""

import re
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QMessageBox, QListWidget, QListWidgetItem,
    QTextEdit, QGroupBox, QSplitter, QWidget, QFormLayout,
    QCheckBox, QSpinBox, QComboBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QObject
from PyQt5.QtGui import QFont, QPalette, QColor

from src.utils.log_config import setup_logger
from .config_sync_manager import ConfigSyncManager


class ValidationResult:
    """验证结果类"""
    
    def __init__(self, is_valid: bool = True, error_message: str = "", suggestions: List[str] = None):
        self.is_valid = is_valid
        self.error_message = error_message
        self.suggestions = suggestions or []


class HeaderEditManager(QObject):
    """表头编辑管理器

    提供表头双击编辑功能的完整管理
    """

    # 信号定义
    mapping_updated = pyqtSignal(str, str, str)  # table_name, field_name, new_display_name
    edit_started = pyqtSignal(str, str)  # table_name, field_name
    edit_completed = pyqtSignal(str, str, str)  # table_name, old_name, new_name
    edit_cancelled = pyqtSignal(str, str)  # table_name, field_name

    def __init__(self, config_sync_manager: ConfigSyncManager = None):
        """初始化表头编辑管理器

        Args:
            config_sync_manager: 配置同步管理器
        """
        super().__init__()
        self.logger = setup_logger(__name__)
        self.config_sync_manager = config_sync_manager or ConfigSyncManager()
        
        # 编辑状态管理
        self.current_edit_dialog = None
        self.edit_history = []
        self.max_history_size = 50
        
        # 编辑统计
        self.edit_stats = {
            "total_edits": 0,
            "successful_edits": 0,
            "cancelled_edits": 0
        }
        
        self.logger.info("表头编辑管理器初始化完成")
    
    def show_edit_dialog(self, parent, current_name: str, table_name: str, 
                        column_index: int = -1) -> bool:
        """显示字段编辑对话框
        
        Args:
            parent: 父窗口
            current_name: 当前字段名
            table_name: 表名
            column_index: 列索引
            
        Returns:
            bool: 是否成功编辑
        """
        try:
            from PyQt5.QtWidgets import QInputDialog

            # 发送编辑开始信号
            self.edit_started.emit(table_name, current_name)

            # 使用简单输入对话框作为临时实现
            new_name, ok = QInputDialog.getText(
                parent,
                '编辑字段名称',
                f'为字段 "{current_name}" 输入新的显示名称:',
                text=current_name
            )

            if ok and new_name.strip() and new_name.strip() != current_name:
                # 验证新名称
                validation_result = self._validate_field_name(new_name.strip(), table_name)

                if validation_result.is_valid:
                    # 更新配置
                    success = self.config_sync_manager.update_mapping(
                        table_name, current_name, new_name.strip()
                    )

                    if success:
                        # 记录编辑历史
                        self._record_edit_history(table_name, current_name, new_name.strip())

                        # 发送信号
                        self.mapping_updated.emit(table_name, current_name, new_name.strip())
                        self.edit_completed.emit(table_name, current_name, new_name.strip())

                        self.edit_stats["successful_edits"] += 1
                        self.logger.info(f"字段编辑成功: {current_name} -> {new_name.strip()}")
                        return True
                    else:
                        QMessageBox.warning(parent, "保存失败", "字段映射更新失败")
                        self.edit_cancelled.emit(table_name, current_name)
                        self.edit_stats["cancelled_edits"] += 1
                        return False
                else:
                    QMessageBox.warning(parent, "输入错误", validation_result.error_message)
                    self.edit_cancelled.emit(table_name, current_name)
                    self.edit_stats["cancelled_edits"] += 1
                    return False
            else:
                self.edit_cancelled.emit(table_name, current_name)
                self.edit_stats["cancelled_edits"] += 1
                return False
                
        except Exception as e:
            self.logger.error(f"显示编辑对话框失败: {e}")
            self.edit_stats["cancelled_edits"] += 1
            return False
        finally:
            self.edit_stats["total_edits"] += 1
    
    def _validate_field_name(self, field_name: str, table_name: str) -> ValidationResult:
        """验证字段名称
        
        Args:
            field_name: 要验证的字段名
            table_name: 表名
            
        Returns:
            ValidationResult: 验证结果
        """
        field_name = field_name.strip()
        
        # 长度检查
        if len(field_name) < 1:
            return ValidationResult(False, "字段名称不能为空")
        
        if len(field_name) > 50:
            return ValidationResult(False, "字段名称不能超过50个字符")
        
        # 字符检查
        if not re.match(r'^[\w\u4e00-\u9fff\-]+$', field_name):
            return ValidationResult(False, "字段名称只能包含中文、英文、数字、下划线、连字符")
        
        # 系统保留字段检查
        reserved_fields = {
            "id", "table_name", "created_at", "updated_at", "deleted_at",
            "import_time", "data_source", "row_id", "version"
        }
        
        if field_name.lower() in reserved_fields:
            return ValidationResult(False, f"'{field_name}' 是系统保留字段名，请使用其他名称")
        
        return ValidationResult(True)
    
    def _record_edit_history(self, table_name: str, old_name: str, new_name: str):
        """记录编辑历史
        
        Args:
            table_name: 表名
            old_name: 旧字段名
            new_name: 新字段名
        """
        try:
            history_record = {
                "timestamp": datetime.now().isoformat(),
                "table_name": table_name,
                "old_name": old_name,
                "new_name": new_name
            }
            
            self.edit_history.append(history_record)
            
            # 限制历史记录大小
            if len(self.edit_history) > self.max_history_size:
                self.edit_history = self.edit_history[-self.max_history_size:]
            
        except Exception as e:
            self.logger.warning(f"记录编辑历史失败: {e}")
    
    def get_edit_statistics(self) -> Dict[str, Any]:
        """获取编辑统计信息
        
        Returns:
            Dict[str, Any]: 编辑统计
        """
        return {
            **self.edit_stats,
            "success_rate": (
                self.edit_stats["successful_edits"] / max(self.edit_stats["total_edits"], 1) * 100
            ),
            "recent_edits": self.edit_history[-10:] if self.edit_history else []
        }
    
    def get_edit_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取编辑历史
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            List[Dict[str, Any]]: 编辑历史记录
        """
        return self.edit_history[-limit:] if self.edit_history else [] 