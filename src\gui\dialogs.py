"""
月度工资异动处理系统 - 对话框模块

本模块实现系统的各种对话框界面，包括数据导入、系统设置、进度显示、关于等对话框。

主要对话框:
1. DataImportDialog - 数据导入对话框
2. SettingsDialog - 系统设置对话框
3. ProgressDialog - 进度显示对话框
4. AboutDialog - 关于系统对话框
"""

import os
import re
import json
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout,
    QGroupBox, QTabWidget, QWidget, QLabel, QLineEdit, QPushButton,
    QComboBox, QSpinBox, QCheckBox, QTableWidget, QTableWidgetItem,
    QHeaderView, QTextEdit, QProgressBar, QDialogButtonBox,
    QMessageBox, QFileDialog, QScrollArea, QFrame, QSplitter,
    QApplication, QSlider, QRadioButton, QListWidget, QListWidgetItem
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QIcon, QColor

import pandas as pd

from src.utils.log_config import setup_logger
from src.modules.data_import.excel_importer import ExcelImporter
from src.modules.data_import.data_validator import DataValidator
from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.data_import.import_defaults_manager import ImportDefaultsManager
from src.modules.data_import.smart_sheet_matcher import SmartSheetMatcher
from src.gui.widgets.target_selection_widget import TargetSelectionWidget
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager


class DataImportDialog(QDialog):
    """数据导入对话框"""
    
    data_imported = pyqtSignal(dict)  # 数据导入完成信号
    
    def __init__(self, parent=None, dynamic_table_manager: Optional[DynamicTableManager] = None, target_path: str = ""):
        super().__init__(parent)
        self.importer = ExcelImporter()
        self.validator = DataValidator()
        self.dynamic_table_manager = dynamic_table_manager
        self.multi_sheet_importer = MultiSheetImporter(dynamic_table_manager=self.dynamic_table_manager)

        # 初始化新的管理器
        self.defaults_manager = ImportDefaultsManager()
        self.sheet_matcher = SmartSheetMatcher(self.defaults_manager)

        self.raw_df = None
        self.preview_df = None
        self.current_file_path = None
        self.target_path = target_path  # 保存目标路径
        self.logger = setup_logger(__name__)
        self.imported_data = {}
        self.is_multi_sheet_mode = True  # 标记是否为多Sheet模式，默认为True
        
        # 目标选择组件
        self.target_selection_widget = None
        self.final_target_path = ""  # 最终确定的目标路径
        
        self._init_ui()
        self._connect_signals()
        self._apply_default_settings()  # 应用默认设置
        self._setup_tooltips()  # 设置工具提示
        self._setup_shortcuts()  # 设置快捷键
        self.logger.info("数据导入对话框初始化完成。")
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("数据导入")
        self.setMinimumSize(1200, 900)  # 增加尺寸以容纳目标选择组件
        self.resize(1200, 900)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(8, 8, 8, 8)
        
        # 创建目标选择组件（固定在顶部）
        self.target_selection_widget = TargetSelectionWidget(self)
        
        # 如果有传入的目标路径，设置到目标选择组件
        if self.target_path:
            self.target_selection_widget.set_target_from_path(self.target_path)
            self.final_target_path = self.target_path
        else:
            # 使用智能推断的默认路径
            self.final_target_path = self.target_selection_widget.get_target_path_string()
        
        main_layout.addWidget(self.target_selection_widget)
        
        # 创建分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(separator)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 创建滚动内容容器
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建选项卡（放在滚动区域内）
        self._create_import_tabs(scroll_layout)
        
        # 设置滚动区域的内容
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)
        
        # 创建按钮区域（固定在底部，不滚动）
        self._create_button_area(main_layout)
        
        # 设置样式
        self._set_dialog_style()
    
    def _create_import_tabs(self, parent_layout):
        """创建导入选项卡"""
        self.tab_widget = QTabWidget()
        
        # Excel导入选项卡
        self._create_excel_import_tab()
        
        # 字段映射选项卡
        self._create_field_mapping_tab()
        
        # 数据预览选项卡
        self._create_data_preview_tab()
        
        parent_layout.addWidget(self.tab_widget)
    
    def _create_excel_import_tab(self):
        """创建Excel导入选项卡"""
        excel_widget = QWidget()
        excel_layout = QVBoxLayout(excel_widget)
        excel_layout.setSpacing(8)  # 减少组件间距
        
        # 文件选择组
        file_group = QGroupBox("文件选择")
        file_layout = QFormLayout(file_group)
        file_layout.setVerticalSpacing(8)  # 减少垂直间距
        
        # Excel文件
        self.salary_file_edit = QLineEdit()
        self.salary_file_edit.setReadOnly(True)
        self.salary_browse_btn = QPushButton("浏览...")
        self.salary_browse_btn.clicked.connect(lambda: self._browse_file(self.salary_file_edit, "Excel文件 (*.xlsx *.xls)"))
        
        salary_layout = QHBoxLayout()
        salary_layout.addWidget(self.salary_file_edit)
        salary_layout.addWidget(self.salary_browse_btn)
        
        file_layout.addRow("Excel文件:", salary_layout)
        
        excel_layout.addWidget(file_group)
        
        # 数据期间组
        period_group = QGroupBox("数据期间")
        period_layout = QFormLayout(period_group)
        period_layout.setVerticalSpacing(8)
        
        # 数据期间选择
        self.data_period_edit = QLineEdit()
        self.data_period_edit.setPlaceholderText("格式: YYYY-MM (如: 2024-06)")
        from datetime import datetime
        current_period = datetime.now().strftime("%Y-%m")
        self.data_period_edit.setText(current_period)
        period_layout.addRow("数据所属期间:", self.data_period_edit)
        
        # 数据描述
        self.data_description_edit = QLineEdit()
        self.data_description_edit.setPlaceholderText("如: 2024年6月正式工工资")
        period_layout.addRow("数据描述:", self.data_description_edit)
        
        excel_layout.addWidget(period_group)
        
        # 导入选项组（合并数据库设置和导入选项）
        options_group = QGroupBox("导入选项")
        options_layout = QFormLayout(options_group)
        options_layout.setVerticalSpacing(8)
        
        # 目标表名
        self.target_table_combo = QComboBox()
        self.target_table_combo.setEditable(True)
        self.target_table_combo.currentTextChanged.connect(self._on_table_selection_changed)
        self._load_existing_tables()  # 加载现有表名
        options_layout.addRow("目标表名:", self.target_table_combo)
        
        # 工作表选择
        self.sheet_combo = QComboBox()
        self.sheet_combo.currentTextChanged.connect(self._on_sheet_changed)
        options_layout.addRow("工作表:", self.sheet_combo)
        
        # 起始行
        self.start_row_spin = QSpinBox()
        self.start_row_spin.setRange(1, 1000)
        self.start_row_spin.setValue(1)  # 修改默认值为1
        self.start_row_spin.valueChanged.connect(self._on_options_changed)
        options_layout.addRow("起始行:", self.start_row_spin)
        
        # 选项复选框（水平布局）
        checkbox_layout = QHBoxLayout()
        
        # 包含表头
        self.header_check = QCheckBox("第一行为表头")
        self.header_check.setChecked(True)
        self.header_check.stateChanged.connect(self._on_options_changed)
        checkbox_layout.addWidget(self.header_check)
        
        # 跳过空行
        self.skip_empty_check = QCheckBox("跳过空行")
        self.skip_empty_check.setChecked(True)
        self.skip_empty_check.stateChanged.connect(self._on_options_changed)
        checkbox_layout.addWidget(self.skip_empty_check)
        
        checkbox_layout.addStretch()
        options_layout.addRow("选项:", checkbox_layout)
        
        excel_layout.addWidget(options_group)
        
        # 表创建选项组（紧凑布局）
        self.table_creation_group = QGroupBox("表不存在时的处理")
        table_creation_layout = QVBoxLayout(self.table_creation_group)
        table_creation_layout.setSpacing(5)
        
        # 使用水平布局放置选项
        creation_options_layout = QHBoxLayout()
        
        # 使用sheet名创建新表
        self.create_table_with_sheet_name = QCheckBox("使用工作表名")
        self.create_table_with_sheet_name.setChecked(True)
        self.create_table_with_sheet_name.toggled.connect(self._on_table_creation_option_changed)
        creation_options_layout.addWidget(self.create_table_with_sheet_name)
        
        # 使用自定义表名
        self.create_table_with_custom_name = QCheckBox("自定义表名")
        self.create_table_with_custom_name.toggled.connect(self._on_table_creation_option_changed)
        creation_options_layout.addWidget(self.create_table_with_custom_name)
        
        # 不创建新表（仅预览）
        self.no_table_creation = QCheckBox("仅预览")
        creation_options_layout.addWidget(self.no_table_creation)
        
        creation_options_layout.addStretch()
        table_creation_layout.addLayout(creation_options_layout)
        
        # 自定义表名输入
        self.custom_table_name_edit = QLineEdit()
        self.custom_table_name_edit.setPlaceholderText("输入自定义表名...")
        self.custom_table_name_edit.setEnabled(False)
        table_creation_layout.addWidget(self.custom_table_name_edit)
        
        excel_layout.addWidget(self.table_creation_group)
        
        # 多Sheet导入配置组
        multi_sheet_group = QGroupBox("多Sheet导入配置")
        multi_sheet_layout = QVBoxLayout(multi_sheet_group)
        multi_sheet_layout.setSpacing(8)
        
        # 导入模式选择（水平布局）
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("导入模式:"))
        
        # 单Sheet模式
        self.single_sheet_mode = QRadioButton("单Sheet模式")
        self.single_sheet_mode.setChecked(False)  # 修改为不选中
        self.single_sheet_mode.toggled.connect(self._on_import_mode_changed)
        mode_layout.addWidget(self.single_sheet_mode)

        # 多Sheet模式
        self.multi_sheet_mode = QRadioButton("多Sheet模式")
        self.multi_sheet_mode.setChecked(True)  # 修改为默认选中
        self.multi_sheet_mode.toggled.connect(self._on_import_mode_changed)
        mode_layout.addWidget(self.multi_sheet_mode)
        
        mode_layout.addStretch()
        multi_sheet_layout.addLayout(mode_layout)
        
        # 多Sheet策略配置（默认显示）
        self.multi_sheet_config_group = QGroupBox("多Sheet策略配置")
        self.multi_sheet_config_group.setVisible(True)  # 修改为默认显示
        config_layout = QFormLayout(self.multi_sheet_config_group)
        config_layout.setVerticalSpacing(8)
        
        # 导入策略选择
        self.import_strategy_combo = QComboBox()
        self.import_strategy_combo.addItem("合并到单表", "merge_to_single_table")
        self.import_strategy_combo.addItem("分离到多表", "separate_tables")
        self.import_strategy_combo.currentTextChanged.connect(self._on_strategy_changed)
        config_layout.addRow("导入策略:", self.import_strategy_combo)
        
        # 目标表模板选择
        self.table_template_combo = QComboBox()
        self.table_template_combo.addItem("工资数据表", "salary_data")
        self.table_template_combo.addItem("异动记录表", "salary_changes")
        config_layout.addRow("表模板:", self.table_template_combo)
        
        # Sheet配置按钮（水平布局）
        sheet_config_layout = QHBoxLayout()
        
        self.config_sheets_btn = QPushButton("配置Sheet映射")
        self.config_sheets_btn.clicked.connect(self._configure_sheet_mappings)
        sheet_config_layout.addWidget(self.config_sheets_btn)
        
        self.export_config_btn = QPushButton("导出配置模板")
        self.export_config_btn.clicked.connect(self._export_sheet_config)
        sheet_config_layout.addWidget(self.export_config_btn)
        
        sheet_config_layout.addStretch()
        config_layout.addRow("配置:", sheet_config_layout)
        
        # 策略说明
        self.strategy_description = QLabel()
        self.strategy_description.setWordWrap(True)
        self.strategy_description.setStyleSheet("QLabel { color: #666; font-size: 11px; padding: 5px; }")
        self.strategy_description.setMaximumHeight(80)  # 限制高度
        self._update_strategy_description()
        config_layout.addRow("说明:", self.strategy_description)
        
        multi_sheet_layout.addWidget(self.multi_sheet_config_group)
        
        excel_layout.addWidget(multi_sheet_group)
        
        # 预览按钮组
        preview_group = QGroupBox("数据预览")
        preview_layout = QHBoxLayout(preview_group)
        
        # 数据预览按钮
        self.preview_btn = QPushButton("数据预览")
        self.preview_btn.setEnabled(False)
        # 注意：按钮信号连接移到 _connect_signals() 方法中统一管理
        preview_layout.addWidget(self.preview_btn)
        
        # 字段映射按钮
        self.mapping_btn = QPushButton("字段映射")
        self.mapping_btn.setEnabled(False)
        # 注意：按钮信号连接移到 _connect_signals() 方法中统一管理
        preview_layout.addWidget(self.mapping_btn)
        
        preview_layout.addStretch()
        
        excel_layout.addWidget(preview_group)
        
        # 移除底部的addStretch，让内容更紧凑
        # excel_layout.addStretch()
        
        self.tab_widget.addTab(excel_widget, "📁 Excel导入")
    
    def _create_field_mapping_tab(self):
        """创建字段映射选项卡"""
        mapping_widget = QWidget()
        mapping_layout = QVBoxLayout(mapping_widget)
        mapping_layout.setSpacing(8)
        
        # 说明标签（固定在顶部）- 美化版本
        info_label = QLabel("📋 请将Excel列映射到系统字段 (系统字段可以编辑修改)")
        info_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e3f2fd, stop: 1 #bbdefb);
                color: #1565c0;
                padding: 12px 16px;
                border: 1px solid #90caf9;
                border-radius: 8px;
                font-weight: 600;
                font-size: 13px;
                margin: 5px;
            }
        """)
        mapping_layout.addWidget(info_label)
        
        # 操作按钮区域（固定在顶部）
        button_layout = QHBoxLayout()
        
        # 智能映射按钮
        self.auto_map_btn = QPushButton("🔄 智能映射")
        self.auto_map_btn.setToolTip("根据字段名称相似度自动进行映射")
        self.auto_map_btn.clicked.connect(self._auto_map_fields)
        
        # 添加字段按钮
        self.add_field_btn = QPushButton("➕ 添加字段")
        self.add_field_btn.setToolTip("添加新的系统字段映射")
        self.add_field_btn.clicked.connect(self._add_field_mapping)
        
        # 删除字段按钮
        self.remove_field_btn = QPushButton("➖ 删除字段")
        self.remove_field_btn.setToolTip("删除选中的字段映射")
        self.remove_field_btn.clicked.connect(self._remove_field_mapping)
        
        # 重置映射按钮
        self.reset_mapping_btn = QPushButton("🔄 重置")
        self.reset_mapping_btn.setToolTip("重置所有字段映射")
        self.reset_mapping_btn.clicked.connect(self._reset_field_mapping)
        
        button_layout.addWidget(self.auto_map_btn)
        button_layout.addWidget(self.add_field_btn)
        button_layout.addWidget(self.remove_field_btn)
        button_layout.addWidget(self.reset_mapping_btn)
        button_layout.addStretch()
        
        mapping_layout.addLayout(button_layout)
        
        # 创建映射表格的滚动区域
        table_scroll = QScrollArea()
        table_scroll.setWidgetResizable(True)
        table_scroll.setMinimumHeight(400)  # 设置最小高度
        
        # 映射表格
        self.mapping_table = QTableWidget()
        self.mapping_table.setColumnCount(4)
        self.mapping_table.setHorizontalHeaderLabels(["系统字段", "Excel列", "数据预览", "必填"])
        
        # 设置列宽
        header = self.mapping_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Interactive)
        header.setSectionResizeMode(1, QHeaderView.Interactive)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        # 设置表格的最小尺寸
        self.mapping_table.setMinimumHeight(400)
        
        # 添加默认字段映射
        self._init_field_mapping()
        
        # 将表格设置为滚动区域的内容
        table_scroll.setWidget(self.mapping_table)
        mapping_layout.addWidget(table_scroll)
        
        # 存储Excel列信息
        self.excel_columns = []
        self.excel_preview_data = []
        
        self.tab_widget.addTab(mapping_widget, "🔗 字段映射")
    
    def _create_data_preview_tab(self):
        """创建数据预览选项卡 - 美化版本"""
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        preview_layout.setSpacing(10)
        
        # 预览说明标签
        preview_info_label = QLabel("📊 数据预览 - 显示前100行数据")
        preview_info_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f3e5f5, stop: 1 #e1bee7);
                color: #7b1fa2;
                padding: 10px 16px;
                border: 1px solid #ce93d8;
                border-radius: 6px;
                font-weight: 600;
                font-size: 13px;
                margin-bottom: 5px;
            }
        """)
        preview_layout.addWidget(preview_info_label)
        
        # 预览表格
        self.preview_table = QTableWidget()
        self.preview_table.setAlternatingRowColors(True)
        self.preview_table.setSelectionBehavior(QTableWidget.SelectRows)
        preview_layout.addWidget(self.preview_table)
        
        # 统计信息 - 美化版本
        self.stats_label = QLabel("📈 数据统计: 总行数: 0, 有效行数: 0")
        self.stats_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e8f5e8, stop: 1 #c8e6c9);
                color: #2e7d32;
                padding: 8px 12px;
                border: 1px solid #81c784;
                border-radius: 4px;
                font-weight: 500;
                font-size: 12px;
            }
        """)
        preview_layout.addWidget(self.stats_label)
        
        self.tab_widget.addTab(preview_widget, "📊 数据预览")
    
    def _create_button_area(self, parent_layout):
        """创建按钮区域"""
        # 状态消息显示区域
        self.status_message_label = QLabel("")
        self.status_message_label.setAlignment(Qt.AlignCenter)
        self.status_message_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                margin: 5px 0;
                font-size: 12px;
                color: #495057;
            }
        """)
        self.status_message_label.setVisible(False)  # 默认隐藏
        parent_layout.addWidget(self.status_message_label)

        button_layout = QHBoxLayout()

        # 设置按钮（左侧）
        settings_btn = QPushButton("⚙️ 设置")
        settings_btn.setToolTip("配置数据导入的默认设置和匹配规则")
        settings_btn.clicked.connect(self._show_settings)
        button_layout.addWidget(settings_btn)

        button_layout.addStretch()

        # 导入按钮
        import_btn = QPushButton("开始导入")
        import_btn.setDefault(True)
        import_btn.clicked.connect(self._start_import)

        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(import_btn)
        button_layout.addWidget(cancel_btn)

        parent_layout.addLayout(button_layout)
    
    def _set_dialog_style(self):
        """设置对话框样式 - 现代化改进版本"""
        self.setStyleSheet("""
            /* 主对话框样式 */
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
            
            /* 滚动区域样式 */
            QScrollArea {
                border: 1px solid #ced4da;
                border-radius: 6px;
                background-color: #ffffff;
                padding: 2px;
            }
            
            QScrollArea > QWidget > QWidget {
                background-color: #ffffff;
            }
            
            /* 分组框样式 */
            QGroupBox {
                font-weight: 600;
                font-size: 13px;
                color: #495057;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 12px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #343a40;
                background-color: #ffffff;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }
            
            /* 按钮样式 */
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #0d6efd, stop: 1 #0b5ed7);
                color: white;
                border: 1px solid #0a58ca;
                padding: 8px 16px;
                border-radius: 6px;
                min-width: 80px;
                font-size: 13px;
                font-weight: 500;
            }
            
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #0b5ed7, stop: 1 #0a58ca);
                border-color: #0a53be;
            }
            
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #0a58ca, stop: 1 #084298);
                border-color: #073984;
            }
            
            QPushButton:disabled {
                background: #adb5bd;
                color: #6c757d;
                border-color: #ced4da;
            }
            
            /* 特殊按钮样式 */
            QPushButton[text="🔄 智能映射"] {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #198754, stop: 1 #157347);
                border-color: #146c43;
            }
            
            QPushButton[text="🔄 智能映射"]:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #157347, stop: 1 #146c43);
            }
            
            QPushButton[text="➕ 添加字段"] {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #fd7e14, stop: 1 #e8630c);
                border-color: #dc5f00;
            }
            
            QPushButton[text="➕ 添加字段"]:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e8630c, stop: 1 #dc5f00);
            }
            
            QPushButton[text="➖ 删除字段"] {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #dc3545, stop: 1 #c82333);
                border-color: #bd2130;
            }
            
            QPushButton[text="➖ 删除字段"]:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #c82333, stop: 1 #bd2130);
            }
            
            /* 选项卡样式 */
            QTabWidget::pane {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                background-color: #ffffff;
                margin-top: -1px;
            }
            
            QTabWidget::tab-bar {
                alignment: left;
            }
            
            QTabBar::tab {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                border: 1px solid #ced4da;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                color: #495057;
                font-weight: 500;
                font-size: 13px;
            }
            
            QTabBar::tab:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                border-bottom-color: #ffffff;
                color: #0d6efd;
                font-weight: 600;
            }
            
            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f1f3f4);
            }
            
            /* 表格样式 */
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #cfe2ff;
                selection-color: #0d6efd;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-size: 12px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border: none;
                border-bottom: 1px solid #f1f3f4;
            }
            
            QTableWidget::item:selected {
                background-color: #cfe2ff;
                color: #0d6efd;
            }
            
            QTableWidget::item:hover {
                background-color: #e7f1ff;
            }
            
            /* 表头样式 */
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                padding: 10px 8px;
                border: 1px solid #dee2e6;
                border-radius: 0px;
                font-weight: 600;
                font-size: 12px;
                color: #495057;
            }
            
            QHeaderView::section:first {
                border-top-left-radius: 6px;
            }
            
            QHeaderView::section:last {
                border-top-right-radius: 6px;
            }
            
            /* 输入框样式 */
            QComboBox, QLineEdit, QSpinBox {
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: #ffffff;
                font-size: 13px;
                color: #495057;
            }
            
            QComboBox:focus, QLineEdit:focus, QSpinBox:focus {
                border-color: #86b7fe;
                outline: none;
            }
            
            QComboBox:hover, QLineEdit:hover, QSpinBox:hover {
                border-color: #b6d7ff;
            }
            
            /* 下拉箭头样式 */
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left-width: 1px;
                border-left-color: #ced4da;
                border-left-style: solid;
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
            
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #6c757d;
                width: 6px;
                height: 6px;
                border-top: none;
                border-left: none;
                /* transform: rotate(45deg); Removed as it's not standard QSS */
            }
            
            /* 复选框样式 */
            QCheckBox {
                spacing: 8px;
                color: #495057;
                font-size: 13px;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #ced4da;
                border-radius: 4px;
                background-color: #ffffff;
            }
            
            QCheckBox::indicator:hover {
                border-color: #86b7fe;
            }
            
            QCheckBox::indicator:checked {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #0d6efd, stop: 1 #0b5ed7);
                border-color: #0a58ca;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            
            QCheckBox::indicator:checked:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #0b5ed7, stop: 1 #0a58ca);
            }
            
            /* 单选按钮样式 */
            QRadioButton {
                spacing: 8px;
                color: #495057;
                font-size: 13px;
            }
            
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #ced4da;
                border-radius: 9px;
                background-color: #ffffff;
            }
            
            QRadioButton::indicator:hover {
                border-color: #86b7fe;
            }
            
            QRadioButton::indicator:checked {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #0d6efd, stop: 1 #0b5ed7);
                border-color: #0a58ca;
            }
            
            QRadioButton::indicator:checked:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #0b5ed7, stop: 1 #0a58ca);
            }
            
            /* 标签样式 */
            QLabel {
                color: #495057;
                font-size: 13px;
            }
            
            /* 分隔线样式 */
            QFrame[frameShape="4"] {
                color: #dee2e6;
                background-color: #dee2e6;
                margin: 10px 0px;
            }
            
            /* 滚动条样式 */
            QScrollBar:vertical {
                background: #f8f9fa;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            
            QScrollBar::handle:vertical {
                background: #ced4da;
                border-radius: 6px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #adb5bd;
            }
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            
            QScrollBar:horizontal {
                background: #f8f9fa;
                height: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            
            QScrollBar::handle:horizontal {
                background: #ced4da;
                border-radius: 6px;
                min-width: 20px;
            }
            
            QScrollBar::handle:horizontal:hover {
                background: #adb5bd;
            }
            
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }
            
            /* 工具提示样式 */
            QToolTip {
                background-color: #212529;
                color: #ffffff;
                border: 1px solid #495057;
                border-radius: 4px;
                padding: 6px 8px;
                font-size: 12px;
            }
        """)
    
    def _browse_file(self, line_edit: QLineEdit, file_filter: str):
        """浏览文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, "选择文件", "", file_filter)
        if file_path:
            line_edit.setText(file_path)
            if line_edit == self.salary_file_edit:
                self._load_sheet_names(file_path)
    
    def _load_sheet_names(self, file_path: str):
        """加载工作表名称"""
        try:
            # 保存当前文件路径
            self.current_file_path = file_path
            
            # 获取工作表名称
            sheet_names = self.importer.get_sheet_names(file_path)
            
            self.sheet_combo.clear()
            self.sheet_combo.addItems(sheet_names)
            
            # 启用预览和映射按钮
            self.preview_btn.setEnabled(True)
            self.mapping_btn.setEnabled(True)
            
            # 如果是多Sheet模式，显示Sheet列表信息
            if self.is_multi_sheet_mode and len(sheet_names) > 1:
                sheet_info = f"检测到 {len(sheet_names)} 个工作表：\n"
                for i, sheet_name in enumerate(sheet_names, 1):
                    sheet_info += f"{i}. {sheet_name}\n"
                
                QMessageBox.information(self, "多Sheet文件", sheet_info.strip())
            elif len(sheet_names) == 1:
                # 只有一个Sheet，建议使用单Sheet模式
                if self.is_multi_sheet_mode:
                    reply = QMessageBox.question(
                        self, "建议", 
                        "该Excel文件只有一个工作表，建议使用单Sheet模式。\n是否切换到单Sheet模式？",
                        QMessageBox.Yes | QMessageBox.No
                    )
                    if reply == QMessageBox.Yes:
                        self.single_sheet_mode.setChecked(True)

            # 如果已选择人员类别，自动执行工作表匹配
            if hasattr(self, 'target_selection_widget') and self.target_selection_widget:
                current_target = self.target_selection_widget.get_current_target()
                category = current_target.get('category', '')
                if category:
                    self._auto_select_sheet_by_category(category)
                else:
                    # 提示用户先选择人员类别
                    self.show_status_message(
                        "💡 提示：选择人员类别后，系统可以自动匹配最合适的工作表",
                        'info', 4000
                    )

        except Exception as e:
            self.logger.error(f"加载工作表名称失败: {e}")
            QMessageBox.warning(self, "警告", f"无法读取Excel文件: {e}")
    
    def _on_sheet_changed(self):
        """工作表选择变化"""
        # 清空预览数据
        self.preview_table.setRowCount(0)
        self.preview_table.setColumnCount(0)
        self.stats_label.setText("数据统计: 总行数: 0, 有效行数: 0")
    
    def _on_options_changed(self):
        """导入选项变化"""
        # 清空预览数据
        self.preview_table.setRowCount(0)
        self.preview_table.setColumnCount(0)
        self.stats_label.setText("数据统计: 总行数: 0, 有效行数: 0")
    
    def _preview_data(self):
        """预览数据"""
        try:
            if not self.salary_file_edit.text():
                QMessageBox.warning(self, "警告", "请先选择Excel文件")
                return
            
            if not self.sheet_combo.currentText():
                QMessageBox.warning(self, "警告", "请先选择工作表")
                return
            
            # 获取参数
            file_path = self.salary_file_edit.text()
            sheet_name = self.sheet_combo.currentText()
            
            # 预览数据
            preview_data = self.importer.preview_data(
                file_path=file_path,
                sheet_name=sheet_name,
                max_rows=20  # 预览20行
            )
            
            # 更新预览表格
            headers = preview_data['columns']
            data = preview_data['sample_data']
            
            self._update_preview_table(data, headers)
            
            # 切换到数据预览选项卡
            self.tab_widget.setCurrentIndex(2)
            
            QMessageBox.information(self, "预览完成", f"已预览 {len(data)} 行数据")
            
        except Exception as e:
            self.logger.error(f"数据预览失败: {e}")
            QMessageBox.critical(self, "错误", f"数据预览失败: {e}")
    
    def _setup_field_mapping(self):
        """设置字段映射 - 修复Excel列加载问题"""
        try:
            if not self.salary_file_edit.text():
                QMessageBox.warning(self, "警告", "请先选择Excel文件")
                return
            
            if not self.sheet_combo.currentText():
                QMessageBox.warning(self, "警告", "请先选择工作表")
                return
            
            # 获取Excel列名和预览数据
            file_path = self.salary_file_edit.text()
            sheet_name = self.sheet_combo.currentText()
            
            self.logger.info(f"开始加载Excel列信息: {file_path}, 工作表: {sheet_name}")
            
            # 获取列信息和预览数据
            preview_data = self.importer.preview_data(
                file_path=file_path,
                sheet_name=sheet_name,
                max_rows=10  # 获取更多行用于预览
            )
            
            self.excel_columns = preview_data['columns']
            self.excel_preview_data = preview_data['sample_data']
            
            self.logger.info(f"成功加载Excel列: {len(self.excel_columns)} 个列")
            
            # 更新所有下拉框
            self._update_excel_columns_in_combos()
            
            # 尝试智能映射
            self._auto_map_fields()
            
            # 切换到字段映射选项卡
            self.tab_widget.setCurrentIndex(1)
            
            QMessageBox.information(self, "字段映射", f"已成功加载 {len(self.excel_columns)} 个Excel列，并尝试进行智能映射")
            
        except Exception as e:
            self.logger.error(f"设置字段映射失败: {e}")
            QMessageBox.critical(self, "错误", f"设置字段映射失败: {e}")
    
    def _update_excel_columns_in_combos(self):
        """更新所有下拉框中的Excel列选项"""
        try:
            for row in range(self.mapping_table.rowCount()):
                combo = self.mapping_table.cellWidget(row, 1)
                if isinstance(combo, QComboBox):
                    # 保存当前选择
                    current_text = combo.currentText()
                    
                    # 清空并重新添加选项
                    combo.clear()
                    combo.addItem("-- 请选择Excel列 --")
                    
                    if self.excel_columns:
                        combo.addItems(self.excel_columns)
                    
                    # 恢复之前的选择（如果仍然有效）
                    if current_text in self.excel_columns:
                        combo.setCurrentText(current_text)
                    elif current_text and current_text != "-- 请选择Excel列 --":
                        # 如果之前有手动输入的值，保留它
                        combo.setCurrentText(current_text)
                    
            self.logger.info(f"成功更新下拉框Excel列选项: {len(self.excel_columns)} 个列")
            
        except Exception as e:
            self.logger.error(f"更新Excel列下拉框失败: {e}")

    def _auto_map_fields(self):
        """智能自动映射字段"""
        try:
            if not self.excel_columns:
                self.logger.warning("Excel列数据为空，无法进行智能映射")
                return
            
            # 字段映射规则 - 支持模糊匹配
            mapping_rules = {
                "工号": ["工号", "员工号", "emp_id", "employee_id", "编号", "号码", "工作号"],
                "姓名": ["姓名", "员工姓名", "name", "员工名", "真实姓名", "人员姓名"],
                "身份证号": ["身份证号", "身份证", "id_card", "identity_card", "证件号", "身份证件号"],
                "部门": ["部门", "所属部门", "department", "dept", "单位", "科室"],
                "职务": ["职务", "职位", "岗位", "position", "job", "title", "职称"],
                "基本工资": ["基本工资", "基础工资", "basic_salary", "base_salary", "基本薪资", "基本工次"],
                "岗位工资": ["岗位工资", "职位工资", "position_salary", "岗位薪资", "岗位工次"],
                "薪级工资": ["薪级工资", "级别工资", "grade_salary", "薪级薪资", "薪级工次"],
                "绩效工资": ["绩效工资", "绩效薪资", "performance_salary", "绩效奖金", "绩效工次"],
                "津贴补贴": ["津贴补贴", "补贴", "津贴", "allowance", "subsidy", "津补贴"],
                "加班费": ["加班费", "加班工资", "overtime", "加班补贴", "超时费"],
                "奖金": ["奖金", "bonus", "奖励", "年终奖", "季度奖"],
                "应发合计": ["应发合计", "应发总额", "gross_salary", "应发工资", "总计", "合计"],
                "个人所得税": ["个人所得税", "个税", "income_tax", "税款", "所得税"],
                "五险一金个人": ["五险一金", "社保", "公积金", "insurance", "社会保险", "个人社保"],
                "其他扣除": ["其他扣除", "扣款", "deduction", "其他减项", "杂项扣除"],
                "实发工资": ["实发工资", "实发金额", "net_salary", "实际发放", "到手工资", "实发合计"]
            }
            
            mapped_count = 0
            
            for row in range(self.mapping_table.rowCount()):
                field_item = self.mapping_table.item(row, 0)
                excel_combo = self.mapping_table.cellWidget(row, 1)
                
                if not field_item or not isinstance(excel_combo, QComboBox):
                    continue
                
                system_field = field_item.text()
                
                # 查找最佳匹配的Excel列
                best_match = None
                best_score = 0
                
                if system_field in mapping_rules:
                    for excel_col in self.excel_columns:
                        excel_col_clean = excel_col.strip()
                        excel_col_lower = excel_col_clean.lower()
                        
                        for pattern in mapping_rules[system_field]:
                            pattern_lower = pattern.lower()
                            
                            # 完全匹配（优先级最高）
                            if excel_col_lower == pattern_lower:
                                best_match = excel_col_clean
                                best_score = 100
                                break
                            # 包含匹配
                            elif pattern_lower in excel_col_lower:
                                score = 90 if len(excel_col_lower) == len(pattern_lower) else 80
                                if score > best_score:
                                    best_match = excel_col_clean
                                    best_score = score
                            elif excel_col_lower in pattern_lower:
                                score = 75
                                if score > best_score:
                                    best_match = excel_col_clean
                                    best_score = score
                        
                        if best_score == 100:  # 找到完全匹配，跳出循环
                            break
                
                # 如果找到好的匹配，设置下拉框
                if best_match and best_score >= 75:
                    excel_combo.setCurrentText(best_match)
                    mapped_count += 1
                    
                    self.logger.debug(f"智能映射: {system_field} -> {best_match} (匹配度: {best_score})")
                    
                    # 更新预览
                    self._update_preview_for_row(row, best_match)
            
            if mapped_count > 0:
                self.logger.info(f"智能映射完成: {mapped_count} 个字段")
                # 不显示消息框，避免打断用户操作
            else:
                self.logger.info("智能映射未找到匹配字段")
                
        except Exception as e:
            self.logger.error(f"智能映射失败: {e}")
            QMessageBox.warning(self, "警告", f"智能映射失败: {e}")

    def _on_excel_column_changed(self, row: int, column_text: str):
        """处理Excel列选择变化"""
        try:
            if column_text == "-- 请选择Excel列 --" or not column_text:
                # 清空预览
                preview_item = self.mapping_table.item(row, 2)
                if preview_item:
                    preview_item.setText("请选择Excel列")
                return
            
            # 更新数据预览
            self._update_preview_for_row(row, column_text)
            
        except Exception as e:
            self.logger.error(f"处理Excel列变化失败: 行{row}, 列{column_text}, 错误: {e}")

    def _on_required_changed(self, row: int, state: int):
        """处理必填状态变化"""
        try:
            is_required = state == Qt.Checked
            field_item = self.mapping_table.item(row, 0)
            if field_item:
                field_name = field_item.text()
                if is_required:
                    field_item.setBackground(QColor(255, 243, 224))  # 浅橙色背景
                    field_item.setToolTip(f"必填字段: {field_name}")
                else:
                    field_item.setBackground(QColor(255, 255, 255))  # 白色背景
                    field_item.setToolTip(f"可选字段: {field_name}")
                    
                self.logger.debug(f"字段 '{field_name}' 必填状态: {is_required}")
                
        except Exception as e:
            self.logger.error(f"处理必填状态变化失败: 行{row}, 状态{state}, 错误: {e}")

    def _update_preview_for_row(self, row: int, excel_column: str):
        """更新指定行的数据预览"""
        try:
            preview_item = self.mapping_table.item(row, 2)
            if not preview_item:
                return
            
            # 如果有预览数据，显示前几个值
            if self.excel_preview_data and excel_column in self.excel_columns:
                col_index = self.excel_columns.index(excel_column)
                
                # 获取前3个非空值作为预览
                preview_values = []
                for data_row in self.excel_preview_data[:5]:  # 最多检查5行
                    if col_index < len(data_row):
                        value = data_row[col_index]
                        if value is not None and str(value).strip():
                            preview_values.append(str(value).strip())
                            if len(preview_values) >= 3:
                                break
                
                if preview_values:
                    preview_text = " | ".join(preview_values)
                    # 限制预览文本长度
                    if len(preview_text) > 50:
                        preview_text = preview_text[:47] + "..."
                    preview_item.setText(preview_text)
                    preview_item.setToolTip(f"Excel列 '{excel_column}' 的数据预览")
                else:
                    preview_item.setText("(该列无数据)")
            else:
                preview_item.setText("等待数据加载...")
                
        except Exception as e:
            self.logger.error(f"更新预览失败: 行{row}, 列{excel_column}, 错误: {e}")

    def _add_field_mapping(self):
        """添加新的字段映射"""
        try:
            current_row_count = self.mapping_table.rowCount()
            self.mapping_table.insertRow(current_row_count)
            
            # 系统字段
            field_item = QTableWidgetItem("新字段")
            field_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsEditable)
            field_item.setToolTip("双击可编辑字段名称")
            self.mapping_table.setItem(current_row_count, 0, field_item)
            
            # Excel列下拉框
            excel_combo = QComboBox()
            excel_combo.setEditable(True)
            excel_combo.addItem("-- 请选择Excel列 --")
            
            # 添加已有的Excel列
            if self.excel_columns:
                excel_combo.addItems(self.excel_columns)
            
            excel_combo.currentTextChanged.connect(
                lambda text, row=current_row_count: self._on_excel_column_changed(row, text)
            )
            
            self.mapping_table.setCellWidget(current_row_count, 1, excel_combo)
            
            # 数据预览
            preview_item = QTableWidgetItem("请选择Excel列")
            preview_item.setFlags(Qt.ItemIsEnabled)
            self.mapping_table.setItem(current_row_count, 2, preview_item)
            
            # 必填复选框
            required_checkbox = QCheckBox()
            required_checkbox.setChecked(False)
            required_checkbox.stateChanged.connect(
                lambda state, row=current_row_count: self._on_required_changed(row, state)
            )
            
            checkbox_widget = QWidget()
            checkbox_layout = QHBoxLayout(checkbox_widget)
            checkbox_layout.addWidget(required_checkbox)
            checkbox_layout.setAlignment(Qt.AlignCenter)
            checkbox_layout.setContentsMargins(0, 0, 0, 0)
            
            self.mapping_table.setCellWidget(current_row_count, 3, checkbox_widget)
            
            # 自动编辑新字段名
            self.mapping_table.editItem(field_item)
            
            self.logger.info(f"添加新字段映射: 行{current_row_count}")
            
        except Exception as e:
            self.logger.error(f"添加字段映射失败: {e}")
            QMessageBox.warning(self, "错误", f"添加字段失败: {e}")

    def _remove_field_mapping(self):
        """删除选中的字段映射"""
        try:
            current_row = self.mapping_table.currentRow()
            if current_row < 0:
                QMessageBox.information(self, "提示", "请先选择要删除的字段映射行")
                return
            
            # 获取字段名用于确认
            field_item = self.mapping_table.item(current_row, 0)
            field_name = field_item.text() if field_item else f"第{current_row + 1}行"
            
            # 确认删除
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除字段映射 '{field_name}' 吗？",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.mapping_table.removeRow(current_row)
                self.logger.info(f"删除字段映射: {field_name} (行{current_row})")
                
        except Exception as e:
            self.logger.error(f"删除字段映射失败: {e}")
            QMessageBox.warning(self, "错误", f"删除字段失败: {e}")

    def _reset_field_mapping(self):
        """重置字段映射"""
        try:
            reply = QMessageBox.question(
                self, "确认重置", 
                "确定要重置所有字段映射吗？这将清除当前的所有映射配置。",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # 清空当前映射表格
                self.mapping_table.setRowCount(0)
                
                # 重新初始化默认映射
                self._init_field_mapping()
                
                # 如果有Excel列数据，重新加载到下拉框
                if self.excel_columns:
                    self._update_excel_columns_in_combos()
                
                self.logger.info("字段映射已重置")
                QMessageBox.information(self, "重置完成", "字段映射已重置为默认配置")
                
        except Exception as e:
            self.logger.error(f"重置字段映射失败: {e}")
            QMessageBox.warning(self, "错误", f"重置失败: {e}")

    def _start_import(self):
        """开始导入"""
        try:
            # 执行智能验证
            validation_result = self._smart_validation()
            if not validation_result['valid']:
                self.show_status_message(validation_result['message'], 'error', 5000)
                return

            if not self.salary_file_edit.text():
                QMessageBox.warning(self, "警告", "请选择Excel文件")
                return

            # 验证数据期间格式
            data_period = self.data_period_edit.text().strip()
            if not data_period:
                QMessageBox.warning(self, "警告", "请输入数据所属期间")
                return
            
            # 验证期间格式 YYYY-MM
            import re
            if not re.match(r'^\d{4}-\d{2}$', data_period):
                QMessageBox.warning(self, "警告", "数据期间格式不正确，请使用YYYY-MM格式（如：2024-06）")
                return
            
            # 解析年月
            year, month = data_period.split('-')
            year = int(year)
            month = int(month)
            
            # 创建进度对话框
            progress = ProgressDialog("正在导入数据...", self)
            progress.show()
            
            # 获取文件路径
            file_path = self.salary_file_edit.text()
            data_description = self.data_description_edit.text().strip()
            
            progress.update_progress(10, "正在验证文件...")
            
            # 验证文件
            file_info = self.importer.validate_file(file_path)
            if not file_info['is_valid']:
                progress.close()
                QMessageBox.critical(self, "错误", f"文件验证失败: {file_info.get('error_message', '未知错误')}")
                return
            
            # 根据导入模式选择处理方式
            if self.is_multi_sheet_mode:
                # 多Sheet导入
                result = self._execute_multi_sheet_import(file_path, year, month, progress)
            else:
                # 单Sheet导入
                result = self._execute_single_sheet_import(file_path, year, month, progress)
            
            progress.update_progress(100, "导入完成")
            progress.close()
            
            if result.get('success'):
                # 发出数据导入完成信号
                self.data_imported.emit(result)
                
                # 显示结果信息
                self._show_import_result(result, data_period)
                
                # 关闭对话框
                self.accept()
            else:
                QMessageBox.critical(self, "导入失败", result.get('error', '未知错误'))
            
        except Exception as e:
            if 'progress' in locals():
                progress.close()
            self.logger.error(f"数据导入失败: {e}")
            QMessageBox.critical(self, "导入失败", f"数据导入失败: {e}")
    
    def _execute_multi_sheet_import(self, file_path: str, year: int, month: int, progress) -> Dict[str, Any]:
        """执行多Sheet导入"""
        try:
            progress.update_progress(30, "正在处理多Sheet数据...")
            
            # 获取导入策略
            strategy = self.import_strategy_combo.currentData()
            table_template = self.table_template_combo.currentData()
            
            # 更新多Sheet导入器的配置
            self.multi_sheet_importer.sheet_configs["import_strategy"] = strategy
            self.multi_sheet_importer.sheet_configs["target_table_template"] = table_template
            
            progress.update_progress(50, "正在执行多Sheet导入...")
            
            # 执行多Sheet导入
            result = self.multi_sheet_importer.import_excel_file(
                file_path=file_path,
                year=year,
                month=month,
                target_table=None  # 使用默认命名
            )
            
            progress.update_progress(80, "正在处理导入结果...")
            
            if result.get('success'):
                # 获取最终目标路径
                final_target_path = ""
                if self.target_selection_widget:
                    final_target_path = self.target_selection_widget.get_target_path_string()
                
                # 补充额外信息
                result.update({
                    'import_mode': 'multi_sheet',
                    'strategy': strategy,
                    'data_period': f"{year}-{month:02d}",
                    'data_description': self.data_description_edit.text().strip() or f"{year}年{month}月工资数据",
                    'file_path': file_path,
                    'target_path': final_target_path  # 确保返回目标路径
                })
                
                self.logger.info(f"多Sheet导入成功: {result}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"多Sheet导入失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _execute_single_sheet_import(self, file_path: str, year: int, month: int, progress) -> Dict[str, Any]:
        """执行单Sheet导入（保持原有逻辑）"""
        try:
            # 验证目标表名
            target_table_name = self._get_target_table_name()
            if not target_table_name and not self.no_table_creation.isChecked():
                return {'success': False, 'error': '请指定目标表名或选择表创建选项'}
            
            sheet_name = self.sheet_combo.currentText() if self.sheet_combo.currentText() else None
            start_row = self.start_row_spin.value() - 1  # 转换为0基索引
            
            progress.update_progress(30, "正在读取数据...")
            
            # 导入数据
            df = self.importer.import_data(
                file_path=file_path,
                sheet_name=sheet_name,
                start_row=start_row,
                max_rows=None
            )
            
            progress.update_progress(50, "正在应用字段映射...")
            
            # 获取字段映射配置
            field_mapping = self.get_field_mapping()
            required_fields = self.get_required_fields()
            
            # 应用字段映射
            if field_mapping:
                self.logger.info(f"应用字段映射: {len(field_mapping)} 个映射")
                df_mapped = df.rename(columns=field_mapping)
                
                # 验证必填字段
                missing_required = []
                for required_field in required_fields:
                    if required_field not in df_mapped.columns:
                        missing_required.append(required_field)
                
                if missing_required:
                    return {
                        'success': False, 
                        'error': f"以下必填字段未完成映射: {', '.join(missing_required)}"
                    }
                
                df = df_mapped
            
            progress.update_progress(70, "正在验证数据...")
            
            # 数据验证
            validation_result = self.validator.validate_dataframe(df)
            
            progress.update_progress(90, "正在处理数据...")
            
            # 转换为字典格式
            headers = df.columns.tolist()
            data = df.to_dict('records')
            
            # 获取最终目标路径
            final_target_path = ""
            if self.target_selection_widget:
                final_target_path = self.target_selection_widget.get_target_path_string()
            
            # 保存导入的数据
            result = {
                'success': True,
                'import_mode': 'single_sheet',
                'dataframe': df,
                'headers': headers,
                'data': data,
                'file_path': file_path,
                'sheet_name': sheet_name,
                'data_period': f"{year}-{month:02d}",
                'data_description': self.data_description_edit.text().strip() or f"{year}年{month}月工资数据",
                'target_table_name': target_table_name,
                'target_path': final_target_path,  # 添加最终目标路径
                'create_new_table': not self.target_table_combo.currentText(),
                'use_sheet_name': self.create_table_with_sheet_name.isChecked(),
                'use_custom_name': self.create_table_with_custom_name.isChecked(),
                'preview_only': self.no_table_creation.isChecked(),
                'validation_result': validation_result,
                'field_mapping': field_mapping,
                'required_fields': required_fields,
                'source_file': file_path,
                'import_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # 保存字段映射配置（使用修复后的逻辑）
            if field_mapping and target_table_name:
                self._save_field_mapping_with_validation(
                    excel_columns=headers,
                    table_name=target_table_name,
                    user_field_mapping=field_mapping
                )

            # 如果是创建新表，并且操作成功，则更新元数据
            if result.get('create_new_table') and target_table_name and self.dynamic_table_manager:
                self.logger.info(f"新表 '{target_table_name}' 创建成功，正在更新元数据...")

                # 从目标路径中解析员工类型
                employee_type = "未知"
                if final_target_path:
                    parts = final_target_path.split(' > ')
                    if len(parts) > 2:
                        employee_type = parts[-1]

                self._update_metadata_after_import(
                    table_name=target_table_name,
                    description=result['data_description'],
                    year=year,
                    month=month,
                    employee_type=employee_type
                )

            return result
            
        except Exception as e:
            self.logger.error(f"单Sheet导入失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _save_field_mapping_with_validation(self, excel_columns: List[str], table_name: str, user_field_mapping: Dict[str, str]):
        """保存字段映射时确保键的一致性（按照解决方案实现）"""
        try:
            # 1. 生成正确的字段映射（DB字段名为键）
            auto_mapping_generator = self.multi_sheet_importer.auto_mapping_generator
            db_field_mapping = auto_mapping_generator.generate_mapping(excel_columns, table_name)

            # 2. 将用户配置的映射转换为DB字段名为键的格式
            validated_mapping = {}

            # 遍历用户配置的映射（Excel列名 -> 显示名）
            for excel_col, display_name in user_field_mapping.items():
                # 获取对应的数据库字段名
                db_field_name = auto_mapping_generator._excel_column_to_db_field(excel_col)
                validated_mapping[db_field_name] = display_name

            # 3. 合并自动生成的映射和用户配置的映射
            final_mapping = {}
            final_mapping.update(db_field_mapping)  # 先添加自动生成的映射
            final_mapping.update(validated_mapping)  # 用户配置的映射优先

            # 4. 验证映射键与实际数据库字段的一致性（如果表已存在）
            if self.dynamic_table_manager:
                try:
                    actual_db_fields = self._get_actual_db_fields(table_name)
                    if actual_db_fields:
                        # 为缺失的数据库字段生成默认显示名
                        for db_field in actual_db_fields:
                            if db_field not in final_mapping:
                                final_mapping[db_field] = self._generate_default_display_name(db_field)

                        self.logger.info(f"为表 {table_name} 验证了 {len(actual_db_fields)} 个数据库字段")
                except Exception as e:
                    self.logger.warning(f"无法获取表 {table_name} 的数据库字段: {e}")

            # 5. 保存验证后的映射配置
            config_sync_manager = self.multi_sheet_importer.config_sync_manager
            success = config_sync_manager.save_mapping(
                table_name=table_name,
                mapping=final_mapping,
                metadata={
                    "auto_generated": False,
                    "user_modified": True,
                    "excel_columns": excel_columns,
                    "user_mapping_count": len(user_field_mapping),
                    "final_mapping_count": len(final_mapping),
                    "generation_timestamp": datetime.now().isoformat()
                }
            )

            if success:
                self.logger.info(f"字段映射保存成功: {table_name}, 共 {len(final_mapping)} 个字段映射")
            else:
                self.logger.error(f"字段映射保存失败: {table_name}")

        except Exception as e:
            self.logger.error(f"保存字段映射失败 {table_name}: {e}")

    def _get_actual_db_fields(self, table_name: str) -> List[str]:
        """获取表的实际数据库字段列表"""
        try:
            if self.dynamic_table_manager:
                # 尝试获取表的列信息
                columns_info = self.dynamic_table_manager.get_table_columns(table_name)
                if columns_info:
                    return [col['name'] for col in columns_info]
        except Exception as e:
            self.logger.debug(f"获取表 {table_name} 的数据库字段失败: {e}")
        return []

    def _generate_default_display_name(self, db_field: str) -> str:
        """为数据库字段生成默认显示名称"""
        # 常见字段映射
        field_display_map = {
            'employee_id': '工号',
            'employee_name': '姓名',
            'department': '部门',
            'position': '职位',
            'basic_salary': '基本工资',
            'position_salary': '岗位工资',
            'grade_salary': '薪级工资',
            'performance': '绩效工资',
            'allowance': '津贴补贴',
            'total_salary': '应发合计',
            'social_insurance': '五险一金个人',
            'data_source': '数据来源',
            'import_time': '导入时间',
            'created_at': '创建时间',
            'updated_at': '更新时间'
        }

        # 检查直接映射
        if db_field in field_display_map:
            return field_display_map[db_field]

        # 如果已经是中文，直接返回
        if any('\u4e00' <= char <= '\u9fff' for char in db_field):
            return db_field

        # 转换下划线命名为更友好的显示
        display_name = db_field.replace('_', ' ').title()
        return display_name

    def _update_metadata_after_import(self, table_name: str, description: str, year: int, month: int, employee_type: str):
        """导入成功后更新元数据"""
        try:
            if not self.dynamic_table_manager:
                self.logger.warning("DynamicTableManager不可用，无法更新元数据")
                return

            metadata = {
                'table_name': table_name,
                'description': description,
                'year': year,
                'month': month,
                'employee_type': employee_type,
                'table_type': 'salary_data',
                'tags': json.dumps([f"{year}年", f"{month}月", employee_type]),
                'source_file': self.get_selected_file(),
                'version': 1
            }

            self.dynamic_table_manager.add_table_metadata(metadata)
            self.logger.info(f"成功为表 '{table_name}' 添加元数据。")

        except Exception as e:
            self.logger.error(f"为表 '{table_name}' 添加元数据失败: {e}", exc_info=True)

    def _show_import_result(self, result: Dict[str, Any], data_period: str):
        """显示导入结果"""
        try:
            if result['import_mode'] == 'multi_sheet':
                # 多Sheet导入结果
                import_stats = result.get('import_stats', {})
                sheets_data = result.get('sheets_data', {}) if result.get('strategy') == 'merge_to_single_table' else result.get('results', {})
                
                if result.get('strategy') == 'merge_to_single_table':
                    # 合并策略结果
                    message = f"""多Sheet导入完成！
                    
期间: {data_period}
策略: 合并到单表
目标表: {result.get('target_table', '未知')}
合并记录数: {result.get('merged_records', 0)}
处理Sheet数: {import_stats.get('processed_sheets', 0)}/{import_stats.get('total_sheets', 0)}

Sheet处理详情："""
                    
                    for sheet_name, record_count in sheets_data.items():
                        message += f"\n• {sheet_name}: {record_count} 行"
                else:
                    # 分离策略结果
                    message = f"""多Sheet导入完成！
                    
期间: {data_period} 
策略: 分离到多表
总记录数: {result.get('total_records', 0)}
处理Sheet数: {import_stats.get('processed_sheets', 0)}/{import_stats.get('total_sheets', 0)}

表创建详情："""
                    
                    for sheet_name, sheet_result in sheets_data.items():
                        if sheet_result.get('success'):
                            message += f"\n• {sheet_name} → {sheet_result.get('table_name', '未知表')}: {sheet_result.get('records', 0)} 行"
                        else:
                            message += f"\n• {sheet_name}: 失败 - {sheet_result.get('error', '未知错误')}"
                
                validation_errors = import_stats.get('validation_errors', 0)
                if validation_errors > 0:
                    message += f"\n\n⚠️ 检测到 {validation_errors} 个数据质量提醒"
                    message += f"\n（这些通常是字段映射建议或数据格式提醒，不影响导入成功）"
                
                QMessageBox.information(self, "多Sheet导入完成", message)
            else:
                # 单Sheet导入结果（保持原有逻辑）
                total_errors = result.get('validation_result', {}).get('total_errors', 0)
                table_msg = f"目标表: {result.get('target_table_name')}" if result.get('target_table_name') else "仅预览模式"
                field_mapping = result.get('field_mapping', {})
                mapping_msg = f"应用了 {len(field_mapping)} 个字段映射" if field_mapping else "未使用字段映射"
                
                data_count = len(result.get('data', []))
                
                message = f"""数据导入完成！

期间: {data_period}
{table_msg}
导入 {data_count} 行数据
{mapping_msg}"""
                
                if total_errors > 0:
                    message += f"\n发现 {total_errors} 个验证警告"
                    QMessageBox.warning(self, "导入完成", message)
                else:
                    QMessageBox.information(self, "导入成功", message)
            
        except Exception as e:
            self.logger.error(f"显示导入结果失败: {e}")
            QMessageBox.information(self, "导入完成", f"导入完成，但显示结果时出现错误: {e}")

    def get_field_mapping(self) -> Dict[str, str]:
        """获取当前的字段映射配置
        
        Returns:
            Dict[str, str]: Excel列名 -> 系统字段名 的映射字典
        """
        try:
            mapping = {}
            for row in range(self.mapping_table.rowCount()):
                field_item = self.mapping_table.item(row, 0)
                excel_combo = self.mapping_table.cellWidget(row, 1)
                
                if field_item and isinstance(excel_combo, QComboBox):
                    system_field = field_item.text().strip()
                    excel_column = excel_combo.currentText().strip()
                    
                    if system_field and excel_column and excel_column != "-- 请选择Excel列 --":
                        mapping[excel_column] = system_field
            
            return mapping
            
        except Exception as e:
            self.logger.error(f"获取字段映射失败: {e}")
            return {}

    def get_required_fields(self) -> List[str]:
        """获取必填字段列表"""
        try:
            required_fields = []
            for row in range(self.mapping_table.rowCount()):
                field_item = self.mapping_table.item(row, 0)
                required_widget = self.mapping_table.cellWidget(row, 3)
                
                if field_item and required_widget:
                    checkbox = required_widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        system_field = field_item.text().strip()
                        if system_field:
                            required_fields.append(system_field)
            
            return required_fields
            
        except Exception as e:
            self.logger.error(f"获取必填字段失败: {e}")
            return []

    def _init_field_mapping(self):
        """初始化字段映射 - 使用ConfigSyncManager的模板（修复版本）"""
        try:
            # 尝试从ConfigSyncManager获取模板
            default_fields = self._get_template_fields()

            if not default_fields:
                # 如果没有模板，使用备用的默认字段列表
                self.logger.warning("未找到字段模板，使用默认字段列表")
                default_fields = self._get_fallback_fields()

        except Exception as e:
            self.logger.error(f"加载字段模板失败: {e}")
            default_fields = self._get_fallback_fields()

    def _get_template_fields(self):
        """从ConfigSyncManager获取模板字段"""
        try:
            # 获取所有可用的模板
            config_manager = self.multi_sheet_importer.config_sync_manager
            config = config_manager._load_config_file()
            templates = config.get("field_templates", {})

            if not templates:
                return None

            # 优先使用"全部在职人员工资表"模板，因为它最通用
            preferred_templates = ["全部在职人员工资表", "退休人员工资表", "A岗职工", "离休人员工资表"]

            selected_template = None
            for template_name in preferred_templates:
                if template_name in templates:
                    selected_template = templates[template_name]
                    self.logger.info(f"使用字段模板: {template_name}")
                    break

            if not selected_template:
                # 如果没有找到首选模板，使用第一个可用的
                template_name = list(templates.keys())[0]
                selected_template = templates[template_name]
                self.logger.info(f"使用字段模板: {template_name}")

            # 将模板转换为字段列表格式
            template_fields = []
            required_fields = {"employee_id", "employee_name"}  # 必填字段

            for db_field, display_name in selected_template.items():
                is_required = db_field in required_fields
                template_fields.append({
                    "name": display_name,
                    "db_field": db_field,
                    "required": is_required
                })

            return template_fields

        except Exception as e:
            self.logger.error(f"获取模板字段失败: {e}")
            return None

    def _get_fallback_fields(self):
        """获取备用的默认字段列表"""
        return [
            {"name": "工号", "required": True},
            {"name": "姓名", "required": True},
            {"name": "身份证号", "required": False},
            {"name": "部门", "required": False},
            {"name": "职务", "required": False},
            {"name": "基本工资", "required": False},
            {"name": "岗位工资", "required": False},
            {"name": "薪级工资", "required": False},
            {"name": "绩效工资", "required": False},
            {"name": "津贴补贴", "required": False},
            {"name": "加班费", "required": False},
            {"name": "奖金", "required": False},
            {"name": "应发合计", "required": False},
            {"name": "个人所得税", "required": False},
            {"name": "五险一金个人", "required": False},
            {"name": "其他扣除", "required": False},
            {"name": "实发工资", "required": False}
        ]

        # 设置表格行数和内容
        self._setup_mapping_table(default_fields)

    def _setup_mapping_table(self, default_fields):
        """设置字段映射表格"""
        self.mapping_table.setRowCount(len(default_fields))
        
        for i, field_info in enumerate(default_fields):
            # 系统字段 - 设置为可编辑
            field_item = QTableWidgetItem(field_info["name"])
            field_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsEditable)  # 可编辑
            field_item.setToolTip("双击可编辑字段名称")
            if field_info["required"]:
                field_item.setBackground(QColor(255, 243, 224))  # 必填字段浅橙色背景
            self.mapping_table.setItem(i, 0, field_item)
            
            # Excel列下拉框 - 改进版本
            excel_combo = QComboBox()
            excel_combo.setEditable(True)  # 允许手动输入
            excel_combo.addItem("-- 请选择Excel列 --")  # 默认空选项
            excel_combo.setToolTip("选择对应的Excel列名，也可以手动输入")
            
            # 连接下拉框变化信号
            excel_combo.currentTextChanged.connect(
                lambda text, row=i: self._on_excel_column_changed(row, text)
            )
            
            self.mapping_table.setCellWidget(i, 1, excel_combo)
            
            # 数据预览
            preview_item = QTableWidgetItem("请先选择Excel列")
            preview_item.setFlags(Qt.ItemIsEnabled)  # 只读
            preview_item.setToolTip("显示映射后的数据预览")
            self.mapping_table.setItem(i, 2, preview_item)
            
            # 必填标志
            required_checkbox = QCheckBox()
            required_checkbox.setChecked(field_info["required"])
            required_checkbox.setToolTip("是否为必填字段")
            
            # 必填字段变化信号
            required_checkbox.stateChanged.connect(
                lambda state, row=i: self._on_required_changed(row, state)
            )
            
            # 将复选框放在一个居中的容器中
            checkbox_widget = QWidget()
            checkbox_layout = QHBoxLayout(checkbox_widget)
            checkbox_layout.addWidget(required_checkbox)
            checkbox_layout.setAlignment(Qt.AlignCenter)
            checkbox_layout.setContentsMargins(0, 0, 0, 0)
            
            self.mapping_table.setCellWidget(i, 3, checkbox_widget)
        
        # 调整行高
        self.mapping_table.verticalHeader().setDefaultSectionSize(35)
        
        self.logger.info(f"初始化字段映射表格: {len(default_fields)} 个默认字段")

    def _update_preview_table(self, data, headers):
        """更新预览表格"""
        try:
            if not data:
                return
                
            # 设置表格行列数
            self.preview_table.setRowCount(min(len(data), 100))  # 最多显示100行
            self.preview_table.setColumnCount(len(headers))
            self.preview_table.setHorizontalHeaderLabels(headers)
            
            # 填充数据
            for row_idx, row_data in enumerate(data[:100]):  # 最多显示100行
                for col_idx, header in enumerate(headers):
                    value = row_data.get(header, '')
                    if value is not None:
                        self.preview_table.setItem(row_idx, col_idx, QTableWidgetItem(str(value)))
            
            # 调整列宽
            self.preview_table.resizeColumnsToContents()
            
            # 更新统计信息 - 美化版本
            total_rows = len(data)
            display_rows = min(total_rows, 100)
            self.stats_label.setText(f"📈 数据统计: 总行数: {total_rows}, 显示行数: {display_rows}")
            
        except Exception as e:
            self.logger.error(f"更新预览表格失败: {e}")

    def get_imported_data(self):
        """获取导入的数据"""
        return self.imported_data

    def get_selected_file(self):
        """获取选择的文件路径"""
        return self.salary_file_edit.text() if hasattr(self, 'salary_file_edit') else ""

    def _load_existing_tables(self):
        """加载现有数据库表名"""
        try:
            if self.dynamic_table_manager:
                # 使用正确的方法名 get_table_list
                tables_info = self.dynamic_table_manager.get_table_list(table_type='salary_data')
                # 注意：返回的字典key是 'table_name'
                existing_tables = sorted([table['table_name'] for table in tables_info])
            else:
                self.logger.warning("DynamicTableManager not available, cannot load existing tables.")
                existing_tables = []
            
            current_selection = self.target_table_combo.currentText()
            self.target_table_combo.clear()
            self.target_table_combo.addItem("")  # 空选项
            self.target_table_combo.addItems(existing_tables)
            
            # 恢复之前的选择
            if current_selection in existing_tables:
                self.target_table_combo.setCurrentText(current_selection)

        except Exception as e:
            self.logger.error(f"加载现有表名失败: {e}", exc_info=True)
    
    def _on_table_selection_changed(self):
        """表名选择变化"""
        selected_table = self.target_table_combo.currentText()
        if selected_table:
            # 如果选择了现有表，禁用表创建选项
            self.table_creation_group.setEnabled(False)
        else:
            # 如果没有选择表，启用表创建选项
            self.table_creation_group.setEnabled(True)
    
    def _on_table_creation_option_changed(self):
        """表创建选项变化"""
        if self.create_table_with_custom_name.isChecked():
            self.custom_table_name_edit.setEnabled(True)
            self.create_table_with_sheet_name.setChecked(False)
            self.no_table_creation.setChecked(False)
        elif self.create_table_with_sheet_name.isChecked():
            self.custom_table_name_edit.setEnabled(False)
            self.create_table_with_custom_name.setChecked(False)
            self.no_table_creation.setChecked(False)
        elif self.no_table_creation.isChecked():
            self.custom_table_name_edit.setEnabled(False)
            self.create_table_with_custom_name.setChecked(False)
            self.create_table_with_sheet_name.setChecked(False)
    
    def _get_target_table_name(self):
        """获取目标表名"""
        # 如果选择了现有表
        if self.target_table_combo.currentText():
            return self.target_table_combo.currentText()
        
        # 如果使用工作表名
        if self.create_table_with_sheet_name.isChecked():
            sheet_name = self.sheet_combo.currentText()
            if sheet_name:
                # 清理工作表名作为表名（移除特殊字符）
                import re
                clean_name = re.sub(r'[^\w\u4e00-\u9fff]', '_', sheet_name)
                return f"sheet_{clean_name}"
        
        # 如果使用自定义表名
        if self.create_table_with_custom_name.isChecked():
            custom_name = self.custom_table_name_edit.text().strip()
            if custom_name:
                return custom_name
        
        # 默认返回空
        return ""

    def set_target_path(self, path: str):
        """设置并更新目标路径显示"""
        self.target_path = path
        if self.target_selection_widget:
            self.target_selection_widget.set_target_from_path(path)
        self.final_target_path = path
        self.logger.info(f"数据导入目标路径已设置为: {path}")

    def _connect_signals(self):
        """连接信号和槽"""
        # 连接目标选择组件的信号
        if self.target_selection_widget:
            self.target_selection_widget.target_changed.connect(self._on_target_changed)
            self.target_selection_widget.new_category_added.connect(self._on_new_category_added)
        
        # The "Import" and "Cancel" buttons are connected directly in _create_button_area.
        # There is no self.button_box in this dialog.
        # self.button_box.accepted.connect(self.accept)
        # self.button_box.rejected.connect(self.reject)

        # 注意：这里的按钮连接逻辑可能需要根据UI的实际情况来调整
        # _create_excel_import_tab 中已经连接了 salary_browse_btn
        # self.salary_browse_btn.clicked.connect(lambda: self._browse_file(self.salary_file_edit, "Excel文件 (*.xlsx *.xls)"))
        
        # 连接预览和映射按钮
        if hasattr(self, 'preview_btn'):
            self.preview_btn.clicked.connect(self._preview_data)
        if hasattr(self, 'mapping_btn'):
            self.mapping_btn.clicked.connect(self._setup_field_mapping)

        # 选项变化的连接
        if hasattr(self, 'sheet_combo'):
            self.sheet_combo.currentTextChanged.connect(self._on_sheet_changed)
        if hasattr(self, 'start_row_spin'):
            self.start_row_spin.valueChanged.connect(self._on_options_changed)
        if hasattr(self, 'header_check'):
            self.header_check.stateChanged.connect(self._on_options_changed)
        if hasattr(self, 'skip_empty_check'):
            self.skip_empty_check.stateChanged.connect(self._on_options_changed)
    
    def _on_target_changed(self, target_info: Dict[str, str]):
        """处理目标位置变化"""
        self.final_target_path = self.target_selection_widget.get_target_path_string()
        self.logger.info(f"目标位置已更新: {self.final_target_path}")

        # 自动填充数据期间
        if hasattr(self, 'data_period_edit'):
            year = target_info.get('year', '')
            month = target_info.get('month', '').zfill(2)
            if year and month:
                self.data_period_edit.setText(f"{year}-{month}")

        # 根据人员类别自动选择工作表
        category = target_info.get('category', '')
        if category and self.current_file_path:
            self._auto_select_sheet_by_category(category)

        # 根据目标信息调整导入策略
        self._adjust_import_strategy_by_target(target_info)

    def _apply_default_settings(self):
        """应用默认设置"""
        try:
            # 获取当前选中的人员类别
            current_target = self.target_selection_widget.get_current_target()
            category = current_target.get('category', '')

            # 根据类别获取智能默认设置
            settings = self.defaults_manager.get_smart_defaults_for_category(category)

            # 应用设置（起始行和导入模式已在界面初始化时设置）
            if hasattr(self, 'header_check'):
                self.header_check.setChecked(settings.get('include_header', True))
            if hasattr(self, 'skip_empty_check'):
                self.skip_empty_check.setChecked(settings.get('skip_empty_rows', True))

            # 根据设置调整导入模式
            import_mode = settings.get('import_mode', 'multi_sheet')
            if import_mode == 'single_sheet':
                self.single_sheet_mode.setChecked(True)
                self.is_multi_sheet_mode = False
                self.multi_sheet_config_group.setVisible(False)
            else:
                self.multi_sheet_mode.setChecked(True)
                self.is_multi_sheet_mode = True
                self.multi_sheet_config_group.setVisible(True)

            # 设置多Sheet配置的默认值
            if hasattr(self, 'import_strategy_combo'):
                import_strategy = settings.get('import_strategy', 'separate_tables')
                # 查找对应的选项并设置
                for i in range(self.import_strategy_combo.count()):
                    if self.import_strategy_combo.itemData(i) == import_strategy:
                        self.import_strategy_combo.setCurrentIndex(i)
                        break

            if hasattr(self, 'table_template_combo'):
                table_template = settings.get('table_template', 'salary_data')
                # 查找对应的选项并设置
                for i in range(self.table_template_combo.count()):
                    if self.table_template_combo.itemData(i) == table_template:
                        self.table_template_combo.setCurrentIndex(i)
                        break

            self.logger.info(f"已应用默认设置: {settings}")

        except Exception as e:
            self.logger.error(f"应用默认设置失败: {e}")

    def _auto_select_sheet_by_category(self, category: str):
        """根据人员类别自动选择工作表"""
        if not self.current_file_path or not category:
            return

        try:
            # 显示匹配进度
            self.show_status_message("🔍 正在分析工作表并执行智能匹配...", 'info', 0)

            # 获取当前文件的所有工作表
            sheet_names = self.importer.get_sheet_names(self.current_file_path)
            if not sheet_names:
                self.show_status_message("⚠️ 未找到工作表", 'warning', 3000)
                return

            # 使用智能匹配器查找最佳匹配
            match_result = self.sheet_matcher.find_best_match(category, sheet_names)

            if match_result:
                # 自动选择匹配的工作表
                self.sheet_combo.setCurrentText(match_result.sheet_name)

                # 记录用户选择（用于学习）
                self.sheet_matcher.record_user_choice(category, match_result.sheet_name)

                # 显示匹配信息
                match_info = f"根据人员类别 '{category}' 自动选择工作表: {match_result.sheet_name}"
                if match_result.match_type != 'exact':
                    match_info += f" (匹配类型: {match_result.match_type}, 得分: {match_result.score:.2f})"

                self.logger.info(match_info)

                # 显示用户提示
                self.show_status_message(match_info, 'success', 3000)
            else:
                # 尝试获取匹配建议
                suggestions = self.sheet_matcher.get_match_suggestions(category, sheet_names, top_n=3)
                if suggestions:
                    self._show_match_suggestions(category, suggestions)
                else:
                    no_match_msg = f"未找到与人员类别 '{category}' 匹配的工作表，请手动选择"
                    self.logger.info(no_match_msg)
                    self.show_status_message(no_match_msg, 'warning', 4000)

        except Exception as e:
            error_msg = f"自动选择工作表失败: {e}"
            self.logger.error(error_msg)
            self.show_status_message(error_msg, 'error', 5000)

    def _adjust_import_strategy_by_target(self, target_info: Dict[str, str]):
        """根据目标信息调整导入策略"""
        try:
            category = target_info.get('category', '')

            # 根据人员类别调整导入策略
            if category in ['离休人员', '退休人员']:
                # 离退休人员数据通常较少，建议单Sheet模式
                if self.is_multi_sheet_mode:
                    suggestion = f"检测到人员类别为 '{category}'，建议使用单Sheet模式以获得更好的导入效果"
                    self.logger.info(suggestion)
                    self.show_status_message(suggestion, 'info', 4000)
            elif category in ['临时工', '实习生']:
                # 临时人员可能需要特殊处理
                if hasattr(self, 'create_table_with_custom_name'):
                    self.create_table_with_custom_name.setChecked(True)
                    suggestion = f"检测到人员类别为 '{category}'，已自动选择自定义表名模式"
                    self.logger.info(suggestion)
                    self.show_status_message(suggestion, 'info', 3000)

        except Exception as e:
            self.logger.error(f"调整导入策略失败: {e}")

    def _show_match_suggestions(self, category: str, suggestions: List):
        """显示匹配建议对话框"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QListWidget, QListWidgetItem

            dialog = QDialog(self)
            dialog.setWindowTitle("智能匹配建议")
            dialog.setModal(True)
            dialog.resize(400, 300)

            layout = QVBoxLayout(dialog)

            # 说明文本
            info_label = QLabel(f"未找到与人员类别 '{category}' 的精确匹配，以下是系统推荐的选项：")
            info_label.setWordWrap(True)
            layout.addWidget(info_label)

            # 建议列表
            suggestion_list = QListWidget()
            for i, suggestion in enumerate(suggestions):
                item_text = f"{suggestion.sheet_name} (匹配度: {suggestion.score:.1%})"
                item = QListWidgetItem(item_text)
                item.setData(32, suggestion.sheet_name)  # 存储工作表名称
                suggestion_list.addItem(item)

            suggestion_list.setCurrentRow(0)  # 默认选中第一个
            layout.addWidget(suggestion_list)

            # 按钮区域
            button_layout = QHBoxLayout()

            use_suggestion_btn = QPushButton("使用选中项")
            use_suggestion_btn.setDefault(True)
            manual_select_btn = QPushButton("手动选择")
            cancel_btn = QPushButton("取消")

            button_layout.addWidget(use_suggestion_btn)
            button_layout.addWidget(manual_select_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)

            # 连接信号
            def use_suggestion():
                current_item = suggestion_list.currentItem()
                if current_item:
                    sheet_name = current_item.data(32)
                    self.sheet_combo.setCurrentText(sheet_name)
                    # 记录用户选择
                    self.sheet_matcher.record_user_choice(category, sheet_name)
                    self.show_status_message(f"已选择建议的工作表: {sheet_name}", 'success', 3000)
                dialog.accept()

            def manual_select():
                self.show_status_message("请在工作表下拉框中手动选择", 'info', 3000)
                dialog.reject()

            use_suggestion_btn.clicked.connect(use_suggestion)
            manual_select_btn.clicked.connect(manual_select)
            cancel_btn.clicked.connect(dialog.reject)

            # 双击选择
            suggestion_list.itemDoubleClicked.connect(use_suggestion)

            dialog.exec_()

        except Exception as e:
            self.logger.error(f"显示匹配建议失败: {e}")
            # 降级到简单提示
            no_match_msg = f"未找到与人员类别 '{category}' 匹配的工作表，请手动选择"
            self.show_status_message(no_match_msg, 'warning', 4000)

    def show_status_message(self, message: str, message_type: str = "info", duration: int = 5000):
        """
        显示状态消息

        Args:
            message: 消息内容
            message_type: 消息类型 ('info', 'success', 'warning', 'error')
            duration: 显示持续时间（毫秒），0表示不自动隐藏
        """
        if not hasattr(self, 'status_message_label'):
            return

        # 根据消息类型设置样式
        styles = {
            'info': {
                'background': '#d1ecf1',
                'border': '#bee5eb',
                'color': '#0c5460',
                'icon': 'ℹ️'
            },
            'success': {
                'background': '#d4edda',
                'border': '#c3e6cb',
                'color': '#155724',
                'icon': '✅'
            },
            'warning': {
                'background': '#fff3cd',
                'border': '#ffeaa7',
                'color': '#856404',
                'icon': '⚠️'
            },
            'error': {
                'background': '#f8d7da',
                'border': '#f5c6cb',
                'color': '#721c24',
                'icon': '❌'
            }
        }

        style = styles.get(message_type, styles['info'])

        self.status_message_label.setStyleSheet(f"""
            QLabel {{
                background-color: {style['background']};
                border: 1px solid {style['border']};
                border-radius: 4px;
                padding: 8px;
                margin: 5px 0;
                font-size: 12px;
                color: {style['color']};
            }}
        """)

        self.status_message_label.setText(f"{style['icon']} {message}")
        self.status_message_label.setVisible(True)

        # 如果设置了持续时间，自动隐藏
        if duration > 0:
            QTimer.singleShot(duration, self._hide_status_message)

    def _hide_status_message(self):
        """隐藏状态消息"""
        if hasattr(self, 'status_message_label'):
            self.status_message_label.setVisible(False)

    def _setup_tooltips(self):
        """设置工具提示"""
        try:
            # 为主要组件添加详细的工具提示
            if hasattr(self, 'target_selection_widget'):
                self.target_selection_widget.setToolTip(
                    "选择数据的目标位置。系统会根据您的选择自动优化导入设置。\n"
                    "💡 提示：选择人员类别后，系统会自动匹配最合适的工作表。"
                )

            if hasattr(self, 'salary_file_edit'):
                self.salary_file_edit.setToolTip(
                    "选择要导入的Excel文件。\n"
                    "💡 提示：选择文件后，系统会自动分析工作表并进行智能匹配。"
                )

            if hasattr(self, 'sheet_combo'):
                self.sheet_combo.setToolTip(
                    "选择要导入的工作表。\n"
                    "🤖 智能功能：系统会根据人员类别自动推荐最合适的工作表。\n"
                    "📚 学习功能：系统会记住您的选择偏好。"
                )

            if hasattr(self, 'start_row_spin'):
                self.start_row_spin.setToolTip(
                    "设置数据开始的行号。\n"
                    "💡 提示：默认值为1，如果第一行是表头，请设置为2。"
                )

            if hasattr(self, 'multi_sheet_mode'):
                self.multi_sheet_mode.setToolTip(
                    "多Sheet模式：适合处理包含多个工作表的复杂Excel文件。\n"
                    "🎯 推荐：大部分工资数据导入场景。"
                )

            if hasattr(self, 'single_sheet_mode'):
                self.single_sheet_mode.setToolTip(
                    "单Sheet模式：适合处理简单的单工作表文件。\n"
                    "🎯 推荐：离退休人员等数据量较少的场景。"
                )

            self.logger.info("工具提示设置完成")

        except Exception as e:
            self.logger.error(f"设置工具提示失败: {e}")

    def _setup_shortcuts(self):
        """设置快捷键"""
        try:
            from PyQt5.QtWidgets import QShortcut
            from PyQt5.QtGui import QKeySequence

            # Ctrl+O: 打开文件
            if hasattr(self, 'salary_browse_btn'):
                open_shortcut = QShortcut(QKeySequence("Ctrl+O"), self)
                open_shortcut.activated.connect(self.salary_browse_btn.click)
                self.salary_browse_btn.setToolTip(
                    self.salary_browse_btn.toolTip() + "\n快捷键: Ctrl+O"
                )

            # Ctrl+P: 预览数据
            if hasattr(self, 'preview_btn'):
                preview_shortcut = QShortcut(QKeySequence("Ctrl+P"), self)
                preview_shortcut.activated.connect(self.preview_btn.click)
                self.preview_btn.setToolTip(
                    self.preview_btn.toolTip() + "\n快捷键: Ctrl+P"
                )

            # Ctrl+Enter: 开始导入
            import_shortcut = QShortcut(QKeySequence("Ctrl+Return"), self)
            import_shortcut.activated.connect(self._start_import)

            # F1: 显示帮助
            help_shortcut = QShortcut(QKeySequence("F1"), self)
            help_shortcut.activated.connect(self._show_help)

            # Escape: 取消
            cancel_shortcut = QShortcut(QKeySequence("Escape"), self)
            cancel_shortcut.activated.connect(self.reject)

            self.logger.info("快捷键设置完成")

        except Exception as e:
            self.logger.error(f"设置快捷键失败: {e}")

    def _show_help(self):
        """显示帮助信息"""
        help_text = """
🚀 数据导入智能功能帮助

📋 基本流程：
1. 选择数据目标位置（年份、月份、人员类别）
2. 选择Excel文件
3. 系统自动匹配工作表
4. 确认设置并开始导入

🤖 智能功能：
• 自动工作表匹配：根据人员类别智能推荐工作表
• 智能默认设置：根据类别自动调整导入参数
• 用户偏好学习：记住您的选择习惯

⌨️ 快捷键：
• Ctrl+O: 打开文件
• Ctrl+P: 预览数据
• Ctrl+Enter: 开始导入
• F1: 显示帮助
• Escape: 取消

💡 提示：
• 绿色消息表示成功操作
• 黄色消息表示需要注意的情况
• 蓝色消息表示系统建议
• 红色消息表示错误或警告
        """

        QMessageBox.information(self, "帮助 - 数据导入智能功能", help_text.strip())

    def _show_settings(self):
        """显示设置对话框"""
        try:
            from src.gui.import_settings_dialog import ImportSettingsDialog

            settings_dialog = ImportSettingsDialog(self)
            if settings_dialog.exec_() == QDialog.Accepted:
                # 设置已保存，重新加载默认设置
                self.defaults_manager = ImportDefaultsManager()
                self.sheet_matcher = SmartSheetMatcher(self.defaults_manager)

                # 重新应用默认设置
                self._apply_default_settings()

                self.show_status_message("✅ 设置已更新并应用", 'success', 3000)
                self.logger.info("用户更新了导入设置")

        except Exception as e:
            self.logger.error(f"显示设置对话框失败: {e}")
            QMessageBox.critical(self, "错误", f"无法打开设置对话框: {e}")

    def _smart_validation(self) -> Dict[str, Any]:
        """
        智能验证导入设置

        Returns:
            验证结果字典，包含 'valid' 和 'message' 字段
        """
        try:
            # 检查基本设置
            if not hasattr(self, 'salary_file_edit') or not self.salary_file_edit.text():
                return {'valid': False, 'message': '❌ 请先选择Excel文件'}

            if not hasattr(self, 'sheet_combo') or not self.sheet_combo.currentText():
                return {'valid': False, 'message': '❌ 请选择工作表'}

            # 检查目标设置
            if hasattr(self, 'target_selection_widget'):
                target = self.target_selection_widget.get_current_target()
                if not target.get('category'):
                    return {'valid': False, 'message': '❌ 请选择人员类别'}

            # 智能建议检查
            suggestions = []

            # 检查起始行设置
            if hasattr(self, 'start_row_spin'):
                start_row = self.start_row_spin.value()
                if start_row > 10:
                    suggestions.append(f"⚠️ 起始行设置为{start_row}，请确认这是正确的")

            # 检查导入模式与数据量的匹配
            if hasattr(self, 'is_multi_sheet_mode') and self.current_file_path:
                try:
                    sheet_names = self.importer.get_sheet_names(self.current_file_path)
                    if len(sheet_names) == 1 and self.is_multi_sheet_mode:
                        suggestions.append("💡 建议：文件只有一个工作表，可以考虑使用单Sheet模式")
                    elif len(sheet_names) > 3 and not self.is_multi_sheet_mode:
                        suggestions.append("💡 建议：文件包含多个工作表，建议使用多Sheet模式")
                except:
                    pass

            # 检查人员类别与工作表的匹配度
            if hasattr(self, 'target_selection_widget') and hasattr(self, 'sheet_combo'):
                target = self.target_selection_widget.get_current_target()
                category = target.get('category', '')
                selected_sheet = self.sheet_combo.currentText()

                if category and selected_sheet and self.current_file_path:
                    try:
                        sheet_names = self.importer.get_sheet_names(self.current_file_path)
                        match_result = self.sheet_matcher.find_best_match(category, sheet_names)

                        if match_result and match_result.sheet_name != selected_sheet:
                            if match_result.score > 0.7:
                                suggestions.append(
                                    f"💡 建议：系统推荐工作表 '{match_result.sheet_name}' "
                                    f"(匹配度: {match_result.score:.1%})，当前选择: '{selected_sheet}'"
                                )
                    except:
                        pass

            # 如果有建议，显示给用户
            if suggestions:
                suggestion_text = "\n".join(suggestions)
                self.show_status_message(suggestion_text, 'warning', 6000)

            return {'valid': True, 'message': '✅ 验证通过'}

        except Exception as e:
            self.logger.error(f"智能验证失败: {e}")
            return {'valid': True, 'message': '⚠️ 验证过程中出现问题，但可以继续导入'}

    def _on_new_category_added(self, category_info: Dict[str, str]):
        """处理新增人员类别"""
        self.logger.info(f"新增人员类别: {category_info}")
        # 这里可以添加通知主窗口更新导航栏的逻辑
        # 暂时只记录日志

    def accept(self):
        """处理"开始导入"按钮的点击事件，此方法由_start_import调用。"""
        # 实际的导入逻辑已经移至 _start_import, 这里只负责关闭对话框
        super().accept()

    def reject(self):
        """处理"取消"按钮的点击事件"""
        super().reject()

    def _on_import_mode_changed(self, checked: bool):
        """导入模式变化处理"""
        # QRadioButton的toggled信号会在选中和取消选中时都发射。
        # 我们只在按钮被选中(checked=True)时执行操作，以避免重复执行。
        if not checked:
            return

        if self.multi_sheet_mode.isChecked():
            # 切换到多Sheet模式
            self.is_multi_sheet_mode = True
            self.multi_sheet_config_group.setVisible(True)
            self.sheet_combo.setEnabled(False)  # 禁用单Sheet选择
            self.logger.info("切换到多Sheet导入模式")
        else:
            # 切换到单Sheet模式
            self.is_multi_sheet_mode = False
            self.multi_sheet_config_group.setVisible(False)
            self.sheet_combo.setEnabled(True)  # 启用单Sheet选择
            self.logger.info("切换到单Sheet导入模式")
    
    def _on_strategy_changed(self):
        """导入策略变化处理"""
        self._update_strategy_description()
    
    def _update_strategy_description(self):
        """更新策略说明"""
        if not hasattr(self, 'import_strategy_combo'):
            return
            
        strategy = self.import_strategy_combo.currentData()
        
        if strategy == "merge_to_single_table":
            description = """
            <b>合并到单表策略：</b><br>
            • 将所有Sheet的数据合并到一个表中<br>
            • 自动处理字段映射和数据合并<br>
            • 适用于结构相似的多个Sheet<br>
            • 便于统一查询和报表生成
            """
        elif strategy == "separate_tables":
            description = """
            <b>分离到多表策略：</b><br>
            • 每个Sheet创建独立的数据表<br>
            • 保持数据源的独立性<br>
            • 适用于不同类型的Sheet数据<br>
            • 便于按数据类型进行管理
            """
        else:
            description = "请选择导入策略"
        
        if hasattr(self, 'strategy_description'):
            self.strategy_description.setText(description)
    
    def _configure_sheet_mappings(self):
        """配置Sheet映射"""
        if not self.current_file_path:
            QMessageBox.warning(self, "警告", "请先选择Excel文件")
            return
        
        try:
            # 获取Sheet列表
            sheet_names = self.importer.get_sheet_names(self.current_file_path)
            
            # 创建Sheet配置对话框
            dialog = SheetMappingDialog(self, sheet_names, self.multi_sheet_importer.sheet_configs)
            if dialog.exec_() == QDialog.Accepted:
                # 获取更新后的配置
                updated_configs = dialog.get_configurations()
                
                # 更新多Sheet导入器的配置
                self.multi_sheet_importer.sheet_configs.update(updated_configs)
                
                QMessageBox.information(self, "成功", "Sheet映射配置已更新")
                
        except Exception as e:
            self.logger.error(f"配置Sheet映射失败: {e}")
            QMessageBox.critical(self, "错误", f"配置Sheet映射失败: {e}")
    
    def _export_sheet_config(self):
        """导出Sheet配置模板"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出配置模板", 
                "sheet_config_template.json", 
                "JSON文件 (*.json)"
            )
            
            if file_path:
                success = self.multi_sheet_importer.export_sheet_config_template(file_path)
                if success:
                    QMessageBox.information(self, "成功", f"配置模板已导出到:\n{file_path}")
                else:
                    QMessageBox.warning(self, "失败", "配置模板导出失败")
        
        except Exception as e:
            self.logger.error(f"导出配置模板失败: {e}")
            QMessageBox.critical(self, "错误", f"导出配置模板失败: {e}")


class SettingsDialog(QDialog):
    """系统设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.settings_changed = False
        self._init_ui()
        self._load_settings()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("系统设置")
        self.setMinimumSize(500, 400)
        self.resize(600, 500)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建设置选项卡
        self._create_settings_tabs(main_layout)
        
        # 创建按钮区域
        self._create_button_area(main_layout)
    
    def _create_settings_tabs(self, parent_layout):
        """创建设置选项卡"""
        self.tab_widget = QTabWidget()
        
        # 常规设置
        self._create_general_tab()
        
        # 数据设置
        self._create_data_tab()
        
        # 界面设置
        self._create_ui_tab()
        
        parent_layout.addWidget(self.tab_widget)
    
    def _create_general_tab(self):
        """创建常规设置选项卡"""
        general_widget = QWidget()
        general_layout = QVBoxLayout(general_widget)
        
        # 基本设置组
        basic_group = QGroupBox("基本设置")
        basic_layout = QFormLayout(basic_group)
        
        # 自动保存
        self.auto_save_check = QCheckBox("启用自动保存")
        basic_layout.addRow("", self.auto_save_check)
        
        # 保存间隔
        self.save_interval_spin = QSpinBox()
        self.save_interval_spin.setRange(1, 60)
        self.save_interval_spin.setValue(5)
        self.save_interval_spin.setSuffix(" 分钟")
        basic_layout.addRow("保存间隔:", self.save_interval_spin)
        
        # 备份设置
        self.backup_check = QCheckBox("启用自动备份")
        basic_layout.addRow("", self.backup_check)
        
        general_layout.addWidget(basic_group)
        general_layout.addStretch()
        
        self.tab_widget.addTab(general_widget, "常规")
    
    def _create_data_tab(self):
        """创建数据设置选项卡"""
        data_widget = QWidget()
        data_layout = QVBoxLayout(data_widget)
        
        # 数据库设置组
        db_group = QGroupBox("数据库设置")
        db_layout = QFormLayout(db_group)
        
        # 数据库路径
        self.db_path_edit = QLineEdit()
        db_browse_btn = QPushButton("浏览...")
        db_browse_btn.clicked.connect(self._browse_db_path)
        
        db_path_layout = QHBoxLayout()
        db_path_layout.addWidget(self.db_path_edit)
        db_path_layout.addWidget(db_browse_btn)
        
        db_layout.addRow("数据库路径:", db_path_layout)
        
        # 最大记录数
        self.max_records_spin = QSpinBox()
        self.max_records_spin.setRange(1000, 100000)
        self.max_records_spin.setValue(10000)
        db_layout.addRow("最大记录数:", self.max_records_spin)
        
        data_layout.addWidget(db_group)
        
        # 导入设置组
        import_group = QGroupBox("导入设置")
        import_layout = QFormLayout(import_group)
        
        # 默认编码
        self.encoding_combo = QComboBox()
        self.encoding_combo.addItems(["utf-8", "gbk", "gb2312"])
        import_layout.addRow("默认编码:", self.encoding_combo)
        
        # 错误处理
        self.error_handling_combo = QComboBox()
        self.error_handling_combo.addItems(["跳过", "停止", "询问"])
        import_layout.addRow("错误处理:", self.error_handling_combo)
        
        data_layout.addWidget(import_group)
        data_layout.addStretch()
        
        self.tab_widget.addTab(data_widget, "数据")
    
    def _create_ui_tab(self):
        """创建界面设置选项卡"""
        ui_widget = QWidget()
        ui_layout = QVBoxLayout(ui_widget)
        
        # 界面设置组
        ui_group = QGroupBox("界面设置")
        ui_group_layout = QFormLayout(ui_group)
        
        # 主题
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["默认", "深色", "浅色"])
        ui_group_layout.addRow("主题:", self.theme_combo)
        
        # 字体大小
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(10)
        ui_group_layout.addRow("字体大小:", self.font_size_spin)
        
        # 显示工具栏
        self.toolbar_check = QCheckBox("显示工具栏")
        ui_group_layout.addRow("", self.toolbar_check)
        
        # 显示状态栏
        self.statusbar_check = QCheckBox("显示状态栏")
        ui_group_layout.addRow("", self.statusbar_check)
        
        ui_layout.addWidget(ui_group)
        ui_layout.addStretch()
        
        self.tab_widget.addTab(ui_widget, "界面")
    
    def _create_button_area(self, parent_layout):
        """创建按钮区域"""
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply
        )
        
        button_box.accepted.connect(self._save_and_close)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.Apply).clicked.connect(self._apply_settings)
        
        parent_layout.addWidget(button_box)
    
    def _browse_db_path(self):
        """浏览数据库路径"""
        path = QFileDialog.getExistingDirectory(self, "选择数据库目录")
        if path:
            self.db_path_edit.setText(path)
    
    def _load_settings(self):
        """加载设置"""
        try:
            # 这里从配置文件加载设置
            pass
        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")
    
    def _apply_settings(self):
        """应用设置"""
        try:
            # 这里保存设置到配置文件
            self.settings_changed = True
            QMessageBox.information(self, "提示", "设置已应用")
        except Exception as e:
            self.logger.error(f"应用设置失败: {e}")
            QMessageBox.critical(self, "错误", f"应用设置失败: {e}")
    
    def _save_and_close(self):
        """保存并关闭"""
        self._apply_settings()
        self.accept()


class ProgressDialog(QDialog):
    """进度对话框"""
    
    def __init__(self, title: str = "处理中...", parent=None):
        super().__init__(parent)
        self.canceled = False
        self._init_ui(title)
        
    def _init_ui(self, title: str):
        """初始化界面 - 美化版本"""
        self.setWindowTitle(title)
        self.setMinimumSize(350, 150)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowTitleHint)
        
        # 设置进度对话框样式
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 2px solid #0d6efd;
                border-radius: 10px;
            }
            QLabel {
                color: #495057;
                font-size: 13px;
                font-weight: 500;
            }
            QProgressBar {
                border: 1px solid #ced4da;
                border-radius: 6px;
                text-align: center;
                font-weight: 600;
                font-size: 12px;
                color: #495057;
                background-color: #f8f9fa;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #0d6efd, stop: 1 #0b5ed7);
                border-radius: 5px;
                margin: 1px;
            }
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #dc3545, stop: 1 #c82333);
                color: white;
                border: 1px solid #bd2130;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: 500;
                min-width: 80px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #c82333, stop: 1 #bd2130);
            }
        """)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题标签
        self.title_label = QLabel(f"⏳ {title}")
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title_label.setStyleSheet("font-size: 15px; font-weight: 600; color: #0d6efd; margin-bottom: 5px;")
        layout.addWidget(self.title_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setMinimumHeight(25)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("🔄 准备中...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet("color: #6c757d; font-size: 12px; margin-top: 5px;")
        layout.addWidget(self.status_label)
        
        # 取消按钮
        cancel_btn = QPushButton("❌ 取消")
        cancel_btn.clicked.connect(self._cancel)
        layout.addWidget(cancel_btn)
    
    def update_progress(self, value: int, status: str = ""):
        """更新进度 - 美化版本"""
        self.progress_bar.setValue(value)
        if status:
            # 根据不同阶段显示不同的图标
            if "验证" in status:
                icon = "🔍"
            elif "读取" in status or "加载" in status:
                icon = "📖"
            elif "处理" in status or "导入" in status:
                icon = "⚙️"
            elif "完成" in status:
                icon = "✅"
            elif "保存" in status:
                icon = "💾"
            else:
                icon = "🔄"
            self.status_label.setText(f"{icon} {status}")
        else:
            self.status_label.setText(f"📊 进度: {value}%")
    
    def _cancel(self):
        """取消操作"""
        self.canceled = True
        self.reject()
    
    def wasCanceled(self) -> bool:
        """是否已取消"""
        return self.canceled


class AboutDialog(QDialog):
    """关于对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("关于系统")
        self.setFixedSize(400, 300)
        
        # 主布局
        layout = QVBoxLayout(self)
        
        # 系统图标
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setPixmap(self.style().standardIcon(
            self.style().SP_ComputerIcon).pixmap(64, 64))
        layout.addWidget(icon_label)
        
        # 系统名称
        name_label = QLabel("月度工资异动处理系统")
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        name_font = QFont()
        name_font.setPointSize(16)
        name_font.setBold(True)
        name_label.setFont(name_font)
        layout.addWidget(name_label)
        
        # 版本信息
        version_label = QLabel("版本 1.0.0")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(version_label)
        
        # 描述信息
        desc_text = QTextEdit()
        desc_text.setReadOnly(True)
        desc_text.setMaximumHeight(100)
        desc_text.setText("""
        自动化处理月度工资异动数据，支持Excel数据导入、
        异动识别计算、报告生成等功能。
        
        开发团队：月度工资异动处理系统开发团队
        """)
        layout.addWidget(desc_text)
        
        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        layout.addWidget(ok_btn) 

class SheetMappingDialog(QDialog):
    """Sheet映射配置对话框"""
    
    def __init__(self, parent=None, sheet_names: List[str] = None, current_configs: Dict[str, Any] = None):
        super().__init__(parent)
        self.sheet_names = sheet_names or []
        self.current_configs = current_configs or {}
        self.logger = setup_logger(__name__)
        self._init_ui()
        self._load_configurations()
        
    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("Sheet映射配置")
        self.setMinimumSize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # 说明标签
        info_label = QLabel("""
        <b>Sheet映射配置</b><br>
        为每个Sheet配置字段映射规则、数据类型和验证规则。<br>
        • 启用/禁用特定Sheet的导入<br>
        • 配置字段名映射关系<br>
        • 设置必填字段验证
        """)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 创建Sheet配置选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 为每个Sheet创建配置选项卡
        self.sheet_configs = {}
        for sheet_name in self.sheet_names:
            self._create_sheet_config_tab(sheet_name)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 重置按钮
        reset_btn = QPushButton("重置为默认")
        reset_btn.clicked.connect(self._reset_to_defaults)
        button_layout.addWidget(reset_btn)
        
        button_layout.addStretch()
        
        # 确定和取消按钮
        ok_btn = QPushButton("确定")
        ok_btn.setDefault(True)
        ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(ok_btn)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
    
    def _create_sheet_config_tab(self, sheet_name: str):
        """为指定Sheet创建配置选项卡"""
        tab_widget = QWidget()
        tab_layout = QVBoxLayout(tab_widget)
        
        # Sheet启用状态
        enable_group = QGroupBox("基本设置")
        enable_layout = QFormLayout(enable_group)
        
        enabled_check = QCheckBox(f"启用 {sheet_name} 的导入")
        enabled_check.setChecked(True)
        enable_layout.addRow("", enabled_check)
        
        # 数据类型
        data_type_combo = QComboBox()
        data_type_combo.addItems(["basic_info", "salary_detail", "allowance_detail", "deduction_detail", "misc_data"])
        enable_layout.addRow("数据类型:", data_type_combo)
        
        tab_layout.addWidget(enable_group)
        
        # 字段映射
        mapping_group = QGroupBox("字段映射")
        mapping_layout = QVBoxLayout(mapping_group)
        
        # 映射表格
        mapping_table = QTableWidget()
        mapping_table.setColumnCount(3)
        mapping_table.setHorizontalHeaderLabels(["Excel列名", "系统字段名", "是否必填"])
        
        # 设置表格属性
        header = mapping_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Interactive)
        header.setSectionResizeMode(1, QHeaderView.Interactive)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        # 初始化一些默认映射行
        self._add_default_mapping_rows(mapping_table, sheet_name)
        
        mapping_layout.addWidget(mapping_table)
        
        # 映射操作按钮
        mapping_btn_layout = QHBoxLayout()
        
        add_mapping_btn = QPushButton("添加映射")
        add_mapping_btn.clicked.connect(lambda: self._add_mapping_row(mapping_table))
        mapping_btn_layout.addWidget(add_mapping_btn)
        
        remove_mapping_btn = QPushButton("删除选中")
        remove_mapping_btn.clicked.connect(lambda: self._remove_mapping_row(mapping_table))
        mapping_btn_layout.addWidget(remove_mapping_btn)
        
        mapping_btn_layout.addStretch()
        mapping_layout.addLayout(mapping_btn_layout)
        
        tab_layout.addWidget(mapping_group)
        
        # 保存配置组件引用
        self.sheet_configs[sheet_name] = {
            'enabled_check': enabled_check,
            'data_type_combo': data_type_combo,
            'mapping_table': mapping_table
        }
        
        self.tab_widget.addTab(tab_widget, sheet_name)
    
    def _add_default_mapping_rows(self, table: QTableWidget, sheet_name: str):
        """为表格添加默认映射行"""
        # 根据Sheet名称推断可能的映射
        default_mappings = []
        
        if "基本信息" in sheet_name or "员工" in sheet_name:
            default_mappings = [
                ("工号", "employee_id", True),
                ("姓名", "employee_name", True),
                ("员工姓名", "employee_name", True),
                ("部门", "department", False),
                ("职位", "position", False)
            ]
        elif "工资" in sheet_name or "薪资" in sheet_name:
            default_mappings = [
                ("工号", "employee_id", True),
                ("基本工资", "basic_salary", False),
                ("绩效工资", "performance_bonus", False),
                ("应发合计", "total_salary", False)
            ]
        elif "津贴" in sheet_name or "补贴" in sheet_name:
            default_mappings = [
                ("工号", "employee_id", True),
                ("津贴", "allowance", False),
                ("交通补贴", "transportation_allowance", False),
                ("餐补", "meal_allowance", False)
            ]
        elif "扣款" in sheet_name:
            default_mappings = [
                ("工号", "employee_id", True),
                ("个税", "income_tax", False),
                ("社保", "social_security_deduction", False),
                ("公积金", "housing_fund_deduction", False)
            ]
        else:
            # 通用映射
            default_mappings = [
                ("工号", "employee_id", True),
                ("姓名", "employee_name", True)
            ]
        
        table.setRowCount(len(default_mappings))
        
        for i, (excel_col, system_field, required) in enumerate(default_mappings):
            # Excel列名
            table.setItem(i, 0, QTableWidgetItem(excel_col))
            
            # 系统字段名
            table.setItem(i, 1, QTableWidgetItem(system_field))
            
            # 必填复选框
            required_check = QCheckBox()
            required_check.setChecked(required)
            table.setCellWidget(i, 2, required_check)
    
    def _add_mapping_row(self, table: QTableWidget):
        """添加映射行"""
        row = table.rowCount()
        table.insertRow(row)
        
        # 添加空的单元格
        table.setItem(row, 0, QTableWidgetItem(""))
        table.setItem(row, 1, QTableWidgetItem(""))
        
        # 添加必填复选框
        required_check = QCheckBox()
        table.setCellWidget(row, 2, required_check)
    
    def _remove_mapping_row(self, table: QTableWidget):
        """删除选中的映射行"""
        current_row = table.currentRow()
        if current_row >= 0:
            table.removeRow(current_row)
    
    def _load_configurations(self):
        """加载现有配置"""
        if not self.current_configs.get('sheet_mappings'):
            return
        
        sheet_mappings = self.current_configs['sheet_mappings']
        
        for sheet_name, config in self.sheet_configs.items():
            if sheet_name in sheet_mappings:
                sheet_config = sheet_mappings[sheet_name]
                
                # 设置启用状态
                config['enabled_check'].setChecked(sheet_config.get('enabled', True))
                
                # 设置数据类型
                data_type = sheet_config.get('data_type', 'misc_data')
                index = config['data_type_combo'].findText(data_type)
                if index >= 0:
                    config['data_type_combo'].setCurrentIndex(index)
                
                # 设置字段映射
                field_mappings = sheet_config.get('field_mappings', {})
                required_fields = sheet_config.get('required_fields', [])
                
                table = config['mapping_table']
                table.setRowCount(len(field_mappings))
                
                for i, (excel_col, system_field) in enumerate(field_mappings.items()):
                    table.setItem(i, 0, QTableWidgetItem(excel_col))
                    table.setItem(i, 1, QTableWidgetItem(system_field))
                    
                    required_check = QCheckBox()
                    required_check.setChecked(system_field in required_fields)
                    table.setCellWidget(i, 2, required_check)
    
    def _reset_to_defaults(self):
        """重置为默认配置"""
        reply = QMessageBox.question(
            self, "确认重置", 
            "确定要重置所有配置为默认值吗？\n此操作将清除当前的所有自定义配置。",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 重新创建所有选项卡
            self.tab_widget.clear()
            self.sheet_configs.clear()
            
            for sheet_name in self.sheet_names:
                self._create_sheet_config_tab(sheet_name)
    
    def get_configurations(self) -> Dict[str, Any]:
        """获取当前配置"""
        configs = {'sheet_mappings': {}}
        
        for sheet_name, config in self.sheet_configs.items():
            # 获取基本设置
            enabled = config['enabled_check'].isChecked()
            data_type = config['data_type_combo'].currentText()
            
            # 获取字段映射
            table = config['mapping_table']
            field_mappings = {}
            required_fields = []
            
            for row in range(table.rowCount()):
                excel_col_item = table.item(row, 0)
                system_field_item = table.item(row, 1)
                required_widget = table.cellWidget(row, 2)
                
                if excel_col_item and system_field_item:
                    excel_col = excel_col_item.text().strip()
                    system_field = system_field_item.text().strip()
                    
                    if excel_col and system_field:
                        field_mappings[excel_col] = system_field
                        
                        if isinstance(required_widget, QCheckBox) and required_widget.isChecked():
                            required_fields.append(system_field)
            
            # 构建Sheet配置
            configs['sheet_mappings'][sheet_name] = {
                'enabled': enabled,
                'data_type': data_type,
                'field_mappings': field_mappings,
                'required_fields': required_fields
            }
        
        return configs


class HeaderCustomizationDialog(QDialog):
    """表头自定义对话框"""

    def __init__(self, all_fields: Dict[str, str], selected_fields: List[str], parent=None):
        """初始化表头自定义对话框

        Args:
            all_fields: 所有可用字段 {字段名: 显示名}
            selected_fields: 当前选中的字段列表
            parent: 父窗口
        """
        super().__init__(parent)
        self.all_fields = all_fields
        self.selected_fields = selected_fields
        self.logger = setup_logger(__name__)

        self.setWindowTitle("自定义表头显示")
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)
        self.setModal(True)

        self._init_ui()

    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()

        # 标题和说明
        title_label = QLabel("自定义表头显示")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)

        desc_label = QLabel("请选择要在表格中显示的字段（勾选表示显示）：")
        desc_label.setStyleSheet("color: #666; margin-bottom: 15px;")
        layout.addWidget(desc_label)

        # 搜索框
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索字段：")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入字段名或显示名进行搜索...")
        self.search_input.textChanged.connect(self._filter_fields)
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        layout.addLayout(search_layout)

        # 字段列表
        self.fields_list = QListWidget()
        self.fields_list.setSelectionMode(QListWidget.MultiSelection)
        self._populate_fields_list()
        layout.addWidget(self.fields_list)

        # 统计信息
        self.stats_label = QLabel()
        self._update_stats()
        layout.addWidget(self.stats_label)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 左侧按钮
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self._select_all)
        button_layout.addWidget(select_all_btn)

        deselect_all_btn = QPushButton("取消全选")
        deselect_all_btn.clicked.connect(self._deselect_all)
        button_layout.addWidget(deselect_all_btn)

        # 分隔符
        button_layout.addStretch()

        # 右侧按钮
        ok_btn = QPushButton("确定")
        ok_btn.setDefault(True)
        ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(ok_btn)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

        # 连接信号
        self.fields_list.itemChanged.connect(self._on_item_changed)

    def _populate_fields_list(self):
        """填充字段列表"""
        self.fields_list.clear()

        for field_name, display_name in self.all_fields.items():
            item = QListWidgetItem()

            # 设置显示文本
            if display_name != field_name:
                item.setText(f"{display_name} ({field_name})")
            else:
                item.setText(display_name)

            # 设置数据
            item.setData(Qt.UserRole, field_name)

            # 设置选中状态
            if field_name in self.selected_fields:
                item.setCheckState(Qt.Checked)
            else:
                item.setCheckState(Qt.Unchecked)

            # 设置复选框
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)

            self.fields_list.addItem(item)

    def _filter_fields(self):
        """根据搜索文本过滤字段"""
        search_text = self.search_input.text().lower()

        for i in range(self.fields_list.count()):
            item = self.fields_list.item(i)
            field_name = item.data(Qt.UserRole)
            display_name = self.all_fields.get(field_name, field_name)

            # 检查字段名或显示名是否包含搜索文本
            visible = (search_text in field_name.lower() or
                      search_text in display_name.lower())

            item.setHidden(not visible)

    def _select_all(self):
        """全选所有可见字段"""
        for i in range(self.fields_list.count()):
            item = self.fields_list.item(i)
            if not item.isHidden():
                item.setCheckState(Qt.Checked)
        self._update_stats()

    def _deselect_all(self):
        """取消全选所有可见字段"""
        for i in range(self.fields_list.count()):
            item = self.fields_list.item(i)
            if not item.isHidden():
                item.setCheckState(Qt.Unchecked)
        self._update_stats()

    def _on_item_changed(self, item):
        """处理项目状态变化"""
        self._update_stats()

    def _update_stats(self):
        """更新统计信息"""
        total_fields = len(self.all_fields)
        selected_count = len(self.get_selected_fields())

        self.stats_label.setText(f"已选择 {selected_count} / {total_fields} 个字段")
        self.stats_label.setStyleSheet("color: #666; font-size: 12px;")

    def get_selected_fields(self) -> List[str]:
        """获取用户选择的字段列表

        Returns:
            List[str]: 选中的字段名列表
        """
        selected = []
        for i in range(self.fields_list.count()):
            item = self.fields_list.item(i)
            if item.checkState() == Qt.Checked:
                field_name = item.data(Qt.UserRole)
                selected.append(field_name)
        return selected