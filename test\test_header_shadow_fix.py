#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表头重影修复效果测试脚本

测试三层防护机制是否有效防止表头重影问题：
1. 第一层防护：导航切换时的预清理
2. 第二层防护：表格数据设置时的强制清理
3. 第三层防护：表头管理器的智能检测

作者: 表头重影修复项目组
创建时间: 2025-06-26
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class HeaderShadowFixTester:
    """表头重影修复测试器"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.test_results = []
        
    def setup_application(self):
        """设置应用程序"""
        try:
            # 创建QApplication
            self.app = QApplication(sys.argv)
            
            # 初始化核心管理器
            config_manager = ConfigManager()
            db_manager = DatabaseManager(config_manager=config_manager)
            dynamic_table_manager = DynamicTableManager(db_manager=db_manager)
            
            # 创建主窗口
            self.main_window = PrototypeMainWindow(
                config_manager=config_manager,
                db_manager=db_manager,
                dynamic_table_manager=dynamic_table_manager
            )
            
            logger.info("应用程序设置完成")
            return True
            
        except Exception as e:
            logger.error(f"应用程序设置失败: {e}")
            return False
    
    def test_navigation_switching(self):
        """测试导航切换时的表头重影修复"""
        logger.info("开始测试导航切换时的表头重影修复...")
        
        try:
            # 获取可用的导航路径
            navigation_paths = [
                "工资表 > 2026年 > 11月 > 全部在职人员",
                "工资表 > 2025年 > 06月 > 全部在职人员",
                "工资表 > 2024年 > 12月 > 全部在职人员"
            ]
            
            test_count = 0
            success_count = 0
            
            for path in navigation_paths:
                test_count += 1
                logger.info(f"测试 {test_count}: 切换到 {path}")
                
                try:
                    # 模拟导航切换
                    context = {'auto_selected': False}
                    self.main_window._on_navigation_changed(path, context)
                    
                    # 等待数据加载完成
                    self.app.processEvents()
                    time.sleep(0.5)
                    
                    # 检查表头状态
                    if self.check_header_state():
                        success_count += 1
                        logger.info(f"测试 {test_count}: 成功 - 无表头重影")
                    else:
                        logger.warning(f"测试 {test_count}: 失败 - 检测到表头重影")
                    
                except Exception as e:
                    logger.error(f"测试 {test_count}: 异常 - {e}")
            
            success_rate = (success_count / test_count) * 100 if test_count > 0 else 0
            logger.info(f"导航切换测试完成: {success_count}/{test_count} 成功，成功率: {success_rate:.1f}%")
            
            self.test_results.append({
                'test_name': '导航切换测试',
                'total': test_count,
                'success': success_count,
                'success_rate': success_rate
            })
            
            return success_rate >= 90  # 90%以上成功率认为测试通过
            
        except Exception as e:
            logger.error(f"导航切换测试失败: {e}")
            return False
    
    def check_header_state(self):
        """检查表头状态是否正常（无重影）"""
        try:
            if not hasattr(self.main_window, 'main_workspace'):
                return True
            
            workspace = self.main_window.main_workspace
            if not hasattr(workspace, 'expandable_table'):
                return True
            
            table = workspace.expandable_table
            
            # 获取表头标签
            headers = []
            for i in range(table.columnCount()):
                header_item = table.horizontalHeaderItem(i)
                if header_item:
                    headers.append(header_item.text())
            
            # 检查是否有重复的表头
            unique_headers = set(headers)
            has_duplicates = len(headers) != len(unique_headers)
            
            if has_duplicates:
                duplicates = [h for h in headers if headers.count(h) > 1]
                logger.warning(f"检测到重复表头: {set(duplicates)}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查表头状态失败: {e}")
            return False
    
    def test_header_manager_detection(self):
        """测试表头管理器的检测功能"""
        logger.info("开始测试表头管理器的检测功能...")
        
        try:
            if not hasattr(self.main_window, 'header_manager'):
                logger.warning("表头管理器不存在")
                return False
            
            header_manager = self.main_window.header_manager
            
            # 注册表格到管理器
            self.main_window._register_all_tables_to_header_manager()
            
            # 执行增强版检测
            if hasattr(header_manager, 'enhanced_auto_detect_and_fix_shadows'):
                report = header_manager.enhanced_auto_detect_and_fix_shadows()
                
                logger.info(f"表头管理器检测报告:")
                logger.info(f"  总表格数: {report.get('total_tables', 0)}")
                logger.info(f"  重影表格数: {len(report.get('shadow_tables', {}))}")
                logger.info(f"  修复成功数: {len(report.get('fixed_tables', []))}")
                logger.info(f"  修复失败数: {len(report.get('failed_tables', []))}")
                
                self.test_results.append({
                    'test_name': '表头管理器检测',
                    'total_tables': report.get('total_tables', 0),
                    'shadow_tables': len(report.get('shadow_tables', {})),
                    'fixed_tables': len(report.get('fixed_tables', [])),
                    'failed_tables': len(report.get('failed_tables', []))
                })
                
                return True
            else:
                logger.warning("增强版检测方法不存在")
                return False
                
        except Exception as e:
            logger.error(f"表头管理器检测测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行表头重影修复效果测试...")
        
        if not self.setup_application():
            logger.error("应用程序设置失败，测试终止")
            return False
        
        # 显示主窗口
        self.main_window.show()
        self.app.processEvents()
        time.sleep(1)  # 等待窗口完全加载
        
        # 运行测试
        test_results = []
        
        # 测试1: 导航切换
        result1 = self.test_navigation_switching()
        test_results.append(('导航切换测试', result1))
        
        # 测试2: 表头管理器检测
        result2 = self.test_header_manager_detection()
        test_results.append(('表头管理器检测', result2))
        
        # 输出测试结果
        self.print_test_summary(test_results)
        
        # 关闭应用程序
        self.main_window.close()
        
        return all(result for _, result in test_results)
    
    def print_test_summary(self, test_results):
        """打印测试总结"""
        logger.info("=" * 60)
        logger.info("表头重影修复效果测试总结")
        logger.info("=" * 60)
        
        for test_name, result in test_results:
            status = "✓ 通过" if result else "✗ 失败"
            logger.info(f"{test_name}: {status}")
        
        logger.info("-" * 60)
        
        for result_data in self.test_results:
            logger.info(f"详细结果: {result_data}")
        
        overall_success = all(result for _, result in test_results)
        overall_status = "✓ 所有测试通过" if overall_success else "✗ 部分测试失败"
        
        logger.info("-" * 60)
        logger.info(f"总体结果: {overall_status}")
        logger.info("=" * 60)

def main():
    """主函数"""
    tester = HeaderShadowFixTester()
    success = tester.run_all_tests()
    
    if success:
        logger.info("表头重影修复方案验证成功！")
        sys.exit(0)
    else:
        logger.error("表头重影修复方案验证失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
