import json

with open('state/data/field_mappings.json', 'r', encoding='utf-8') as f:
    config = json.load(f)
    
print('Table mappings:')
for table_name, mapping_data in config.get('table_mappings', {}).items():
    field_count = len(mapping_data.get('field_mappings', {}))
    print(f'  {table_name}: {field_count} fields')
    
if not config.get('table_mappings'):
    print('  (No table mappings found)')
