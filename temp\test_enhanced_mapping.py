#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的字段映射功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_storage.database_manager import DatabaseManager
import pandas as pd

def test_enhanced_mapping():
    """测试增强的字段映射功能"""
    print('=== 测试增强的字段映射功能 ===')
    
    try:
        # 1. 测试ConfigSyncManager的模板功能
        print('\n1. 测试ConfigSyncManager模板功能')
        config_manager = ConfigSyncManager()
        
        # 测试所有模板
        templates = ["离休人员工资表", "退休人员工资表", "全部在职人员工资表", "A岗职工"]
        for template_name in templates:
            template = config_manager.get_template_mapping(template_name)
            if template:
                print(f'✅ {template_name}: {len(template)} 个字段')
            else:
                print(f'❌ {template_name}: 模板加载失败')
        
        # 2. 测试MultiSheetImporter的表类型检测
        print('\n2. 测试表类型检测功能')

        # 创建必要的依赖
        db_manager = DatabaseManager()
        table_manager = DynamicTableManager(db_manager)
        importer = MultiSheetImporter(table_manager)
        
        # 测试不同类型的表头
        test_cases = [
            {
                "sheet_name": "离休人员工资表",
                "headers": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴"],
                "expected": "离休人员工资表"
            },
            {
                "sheet_name": "退休人员工资表", 
                "headers": ["序号", "人员代码", "姓名", "部门名称", "基本退休费", "津贴", "应发工资"],
                "expected": "退休人员工资表"
            },
            {
                "sheet_name": "全部在职人员工资表",
                "headers": ["序号", "工号", "姓名", "部门名称", "2025年岗位工资", "2025年薪级工资", "应发工资"],
                "expected": "全部在职人员工资表"
            },
            {
                "sheet_name": "A岗职工",
                "headers": ["序号", "工号", "姓名", "部门名称", "2025年校龄工资", "津贴", "应发工资"],
                "expected": "A岗职工"
            }
        ]
        
        for test_case in test_cases:
            detected_type = importer._detect_table_type_from_headers(
                test_case["headers"], 
                test_case["sheet_name"]
            )
            if detected_type == test_case["expected"]:
                print(f'✅ {test_case["sheet_name"]}: 检测正确 -> {detected_type}')
            else:
                print(f'❌ {test_case["sheet_name"]}: 检测错误，期望 {test_case["expected"]}，实际 {detected_type}')
        
        # 3. 测试模板适配功能
        print('\n3. 测试模板适配功能')
        template = config_manager.get_template_mapping("退休人员工资表")
        if template:
            excel_headers = ["序号", "人员代码", "姓名", "部门名称", "基本退休费", "津贴", "应发工资", "公积", "备注"]
            adapted_mapping = importer._adapt_template_to_excel_headers(template, excel_headers)
            
            print(f'✅ 模板适配完成: {len(adapted_mapping)} 个字段')
            print('   适配结果示例:')
            for i, (db_field, display_name) in enumerate(list(adapted_mapping.items())[:5]):
                print(f'     {db_field} -> {display_name}')
        
        # 4. 测试字段映射生成和保存
        print('\n4. 测试字段映射生成和保存')
        test_table_name = "salary_data_2025_01_active_employees_test"
        test_headers = ["工号", "姓名", "部门名称", "2025年岗位工资", "2025年薪级工资", "津贴", "应发工资"]
        
        # 检测表类型
        table_type = importer._detect_table_type_from_headers(test_headers, "全部在职人员工资表")
        print(f'   检测到表类型: {table_type}')
        
        if table_type:
            # 应用模板
            success = config_manager.apply_template_to_table(test_table_name, table_type)
            if success:
                print(f'✅ 成功应用模板到表 {test_table_name}')
                
                # 验证映射
                mapping = config_manager.load_mapping(test_table_name)
                if mapping:
                    print(f'✅ 映射验证成功: {len(mapping)} 个字段')
                    print('   映射示例:')
                    for i, (db_field, display_name) in enumerate(list(mapping.items())[:5]):
                        print(f'     {db_field} -> {display_name}')
                else:
                    print('❌ 映射验证失败')
            else:
                print('❌ 模板应用失败')
        
        print('\n=== 测试完成 ===')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_mapping()
