#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试信号机制（非GUI测试）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtCore import QObject, QCoreApplication
from src.modules.data_import.config_sync_manager import ConfigSyncManager
from src.modules.data_import.header_edit_manager import HeaderEditManager

class SignalReceiver(QObject):
    """信号接收器"""
    
    def __init__(self):
        super().__init__()
        self.received_signals = []
    
    def on_mapping_updated(self, table_name: str, field_name: str, new_display_name: str):
        """映射更新信号处理"""
        signal_info = f"mapping_updated: {table_name}.{field_name} -> {new_display_name}"
        self.received_signals.append(signal_info)
        print(f"✅ 接收到信号: {signal_info}")
    
    def on_edit_started(self, table_name: str, field_name: str):
        """编辑开始信号处理"""
        signal_info = f"edit_started: {table_name}.{field_name}"
        self.received_signals.append(signal_info)
        print(f"🔄 接收到信号: {signal_info}")
    
    def on_edit_completed(self, table_name: str, old_name: str, new_name: str):
        """编辑完成信号处理"""
        signal_info = f"edit_completed: {table_name} {old_name} -> {new_name}"
        self.received_signals.append(signal_info)
        print(f"✅ 接收到信号: {signal_info}")
    
    def on_edit_cancelled(self, table_name: str, field_name: str):
        """编辑取消信号处理"""
        signal_info = f"edit_cancelled: {table_name}.{field_name}"
        self.received_signals.append(signal_info)
        print(f"❌ 接收到信号: {signal_info}")

def test_signal_mechanism():
    """测试信号机制"""
    print('=== 测试HeaderEditManager信号机制 ===')
    
    try:
        # 创建QCoreApplication（信号需要）
        app = QCoreApplication([])
        
        # 1. 创建组件
        print('\n1. 创建组件')
        config_manager = ConfigSyncManager()
        header_edit_manager = HeaderEditManager(config_manager)
        signal_receiver = SignalReceiver()
        
        # 2. 连接信号
        print('\n2. 连接信号')
        header_edit_manager.mapping_updated.connect(signal_receiver.on_mapping_updated)
        header_edit_manager.edit_started.connect(signal_receiver.on_edit_started)
        header_edit_manager.edit_completed.connect(signal_receiver.on_edit_completed)
        header_edit_manager.edit_cancelled.connect(signal_receiver.on_edit_cancelled)
        print('✅ 信号连接完成')
        
        # 3. 测试信号发送
        print('\n3. 测试信号发送')
        
        # 准备测试数据
        test_table_name = "test_signal_table"
        success = config_manager.apply_template_to_table(test_table_name, "全部在职人员工资表")
        if success:
            print(f'✅ 测试表 {test_table_name} 模板应用成功')
        
        # 模拟编辑开始
        print('\n   模拟编辑开始...')
        header_edit_manager.edit_started.emit(test_table_name, "employee_id")
        
        # 模拟映射更新
        print('   模拟映射更新...')
        header_edit_manager.mapping_updated.emit(test_table_name, "employee_id", "员工编号")
        
        # 模拟编辑完成
        print('   模拟编辑完成...')
        header_edit_manager.edit_completed.emit(test_table_name, "工号", "员工编号")
        
        # 模拟编辑取消
        print('   模拟编辑取消...')
        header_edit_manager.edit_cancelled.emit(test_table_name, "employee_name")
        
        # 4. 验证信号接收
        print('\n4. 验证信号接收')
        expected_signals = 4
        received_signals = len(signal_receiver.received_signals)
        
        if received_signals == expected_signals:
            print(f'✅ 信号接收测试通过: {received_signals}/{expected_signals}')
            print('   接收到的信号:')
            for i, signal in enumerate(signal_receiver.received_signals, 1):
                print(f'     {i}. {signal}')
        else:
            print(f'❌ 信号接收测试失败: {received_signals}/{expected_signals}')
        
        # 5. 测试ConfigSyncManager的update_mapping方法
        print('\n5. 测试ConfigSyncManager的update_mapping方法')
        
        # 清空之前的信号
        signal_receiver.received_signals.clear()
        
        # 直接调用update_mapping（这应该触发信号）
        success = config_manager.update_mapping(test_table_name, "employee_id", "新员工编号")
        if success:
            print('✅ update_mapping调用成功')
        else:
            print('❌ update_mapping调用失败')
        
        # 检查是否有新信号
        if signal_receiver.received_signals:
            print(f'✅ update_mapping触发了 {len(signal_receiver.received_signals)} 个信号')
        else:
            print('⚠️ update_mapping没有触发信号（这是正常的，因为ConfigSyncManager本身不发送信号）')
        
        print('\n=== 信号机制测试完成 ===')
        
        return received_signals == expected_signals
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_signal_mechanism()
    if success:
        print('\n🎉 所有测试通过！信号机制工作正常。')
    else:
        print('\n⚠️ 部分测试失败，需要检查信号机制。')
